{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/Code/Web%20Projects/Client%20Projects/Cepoka%20Website/cepoka/src/app/context/ActiveLinkContext.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport React, { createContext, useContext, useState } from \"react\";\r\n\r\ninterface ActiveLinkContextProps {\r\n  activeLink: string;\r\n  setActiveLink: (link: string) => void;\r\n}\r\n\r\nconst ActiveLinkContext = createContext<ActiveLinkContextProps | undefined>(undefined);\r\n\r\nexport const ActiveLinkProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {\r\n  const [activeLink, setActiveLink] = useState<string>(\"Home\");\r\n\r\n  return (\r\n    <ActiveLinkContext.Provider value={{ activeLink, setActiveLink }}>\r\n      {children}\r\n    </ActiveLinkContext.Provider>\r\n  );\r\n};\r\n\r\nexport const useActiveLink = () => {\r\n  const context = useContext(ActiveLinkContext);\r\n  if (!context) {\r\n    throw new Error(\"useActiveLink must be used within an ActiveLinkProvider\");\r\n  }\r\n  return context;\r\n};\r\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AASA,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAsC;AAErE,MAAM,qBAA8D,CAAC,EAAE,QAAQ,EAAE;IACtF,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAErD,qBACE,8OAAC,kBAAkB,QAAQ;QAAC,OAAO;YAAE;YAAY;QAAc;kBAC5D;;;;;;AAGP;AAEO,MAAM,gBAAgB;IAC3B,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT"}}, {"offset": {"line": 70, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/Code/Web%20Projects/Client%20Projects/Cepoka%20Website/cepoka/src/app/Components/Nav.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useEffect, useState } from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport Link from \"next/link\";\r\nimport Image from \"next/image\";\r\nimport { usePathname, useRouter } from \"next/navigation\";\r\nimport { useActiveLink } from \"../context/ActiveLinkContext\";\r\nimport { Search } from 'lucide-react';\r\n\r\nconst Nav = () => {\r\n  const { activeLink, setActiveLink } = useActiveLink();\r\n  const [isVisible, setIsVisible] = useState(false);\r\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\r\n  const [searchQuery, setSearchQuery] = useState(\"\");\r\n  const [showSearch, setShowSearch] = useState(false);\r\n  const pathname = usePathname(); // Get the current route\r\n  const router = useRouter();\r\n  const [isMounted, setIsMounted] = useState(false);\r\n\r\n  useEffect(() => {\r\n    setIsMounted(true);\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    if (!isMounted || typeof window === 'undefined') return;\r\n\r\n    const handleScroll = () => {\r\n      const scrollPosition = window?.scrollY;\r\n      setIsVisible(scrollPosition > 300); // Show the navbar after scrolling 300px\r\n    };\r\n\r\n    if (pathname === \"/\") {\r\n      // Add scroll event listener for the home page\r\n      setIsVisible(false); // Initially hide the navbar\r\n      window?.addEventListener(\"scroll\", handleScroll);\r\n      return () => window?.removeEventListener(\"scroll\", handleScroll);\r\n    } else {\r\n      setIsVisible(true); // Show the navbar immediately for other pages\r\n    }\r\n\r\n    // Clean up the event listener\r\n    return () => {\r\n      window.removeEventListener(\"scroll\", handleScroll);\r\n    };\r\n  }, [pathname, isMounted]);\r\n\r\n  // Add this useEffect to sync activeLink with current pathname\r\n  useEffect(() => {\r\n    if (pathname === '/shop') {\r\n      setActiveLink('Shop');\r\n    } else if (pathname === '/') {\r\n      setActiveLink('Home');\r\n    }\r\n  }, [pathname, setActiveLink]);\r\n\r\n  const scrollToSection = (sectionId: string) => {\r\n    if (!isMounted || typeof window === 'undefined') return;\r\n    const element = document.getElementById(sectionId);\r\n    if (element) {\r\n      element.scrollIntoView({ behavior: 'smooth' });\r\n    }\r\n  };\r\n\r\n  const handleNavClick = (link: string) => {\r\n    setActiveLink(link);\r\n    setIsMenuOpen(false);\r\n\r\n    // Don't interfere with admin routes\r\n    if (pathname && pathname.startsWith('/admin')) {\r\n      console.log('Navigation in admin area - not handling nav clicks');\r\n      return;\r\n    }\r\n\r\n    if (pathname === '/') {\r\n      switch (link.toLowerCase()) {\r\n        case 'contact':\r\n          scrollToSection('section7');\r\n          break;\r\n        case 'about':\r\n          scrollToSection('section6');\r\n          break;\r\n        case 'home':\r\n          window.scrollTo({ top: 0, behavior: 'smooth' });\r\n          break;\r\n        case 'shop':\r\n          router.push('/shop');\r\n          break;\r\n      }\r\n    } else {\r\n      if (link.toLowerCase() === 'home') {\r\n        router.push('/');\r\n      } else {\r\n        router.push(`/${link.toLowerCase()}`);\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleSearch = (e?: React.FormEvent) => {\r\n    e?.preventDefault();\r\n    if (searchQuery.trim()) {\r\n      router.push(`/shop?search=${encodeURIComponent(searchQuery.trim())}`);\r\n    }\r\n    setShowSearch(false);\r\n    setSearchQuery(\"\");\r\n  };\r\n\r\n  const menuItemVariants = {\r\n    closed: { opacity: 0, y: -20 },\r\n    open: (i: number) => ({\r\n      opacity: 1,\r\n      y: 0,\r\n      transition: {\r\n        delay: i * 0.1,\r\n        duration: 0.4,\r\n        ease: [0.4, 0, 0.2, 1],\r\n      }\r\n    })\r\n  };\r\n\r\n  // Updated click handler with better area detection\r\n  useEffect(() => {\r\n    const handleClickAway = (event: MouseEvent) => {\r\n      if (!isMenuOpen) return;\r\n\r\n      const target = event.target as HTMLElement;\r\n      const mobileMenu = document.getElementById('mobile-menu-container');\r\n      const hamburgerButton = document.getElementById('hamburger-button');\r\n      const searchForm = document.getElementById('mobile-search-form');\r\n\r\n      // Check if click is within any of our menu components\r\n      const isClickInMenu = mobileMenu?.contains(target);\r\n      const isClickOnButton = hamburgerButton?.contains(target);\r\n      const isClickInSearch = searchForm?.contains(target);\r\n      const isClickOnOverlay = target.classList.contains('menu-overlay');\r\n\r\n      // Only close if clicking outside all menu components and on overlay\r\n      if ((!isClickInMenu && !isClickOnButton && !isClickInSearch) && isClickOnOverlay) {\r\n        setIsMenuOpen(false);\r\n      }\r\n    };\r\n\r\n    document.addEventListener('mousedown', handleClickAway);\r\n    return () => document.removeEventListener('mousedown', handleClickAway);\r\n  }, [isMenuOpen]);\r\n\r\n  return (\r\n    <>\r\n      {/* Updated overlay to match Hero component exactly */}\r\n      {isMenuOpen && (\r\n        <motion.div\r\n          initial={{ opacity: 0 }}\r\n          animate={{ opacity: 1 }}\r\n          exit={{ opacity: 0 }}\r\n          transition={{ duration: 0.3 }}\r\n          className=\"menu-overlay fixed inset-0 bg-black/50 backdrop-blur-sm z-[90]\"\r\n        />\r\n      )}\r\n\r\n      <nav className={`fixed top-0 w-full z-[180] bg-gradient-to-l from-[#87878780] to-transparent  backdrop-blur-[15px] text-white p-6 sm:p-8 transition-transform duration-300 border-b border-black/10 ${isVisible ? \"translate-y-0\" : \"-translate-y-full\"}`}>\r\n        <div className=\"container mx-auto relative\">\r\n          {/* Main nav content */}\r\n          <div className={`flex justify-between items-center relative z-[200] ${isMenuOpen ? 'opacity-0' : 'opacity-100'} transition-opacity duration-300`}>\r\n            {/* Logo and brand name with higher z-index */}\r\n            <Link href={\"/\"} className=\"relative z-[200] flex items-center gap-3\">\r\n              <div className=\"bg-transparent p-1 rounded-full\">\r\n                <Image src=\"/logo.png\" alt=\"Logo\" width={40} height={50} />\r\n              </div>\r\n              <div className=\"block\">\r\n                <motion.span\r\n                  className=\"text-[13px] sm:text-xl font-bold bg-clip-text text-transparent inline-block relative\"\r\n                  style={{\r\n                    backgroundImage: \"linear-gradient(90deg, #1E90FF, #FF69B4)\",\r\n                    backgroundSize: \"200% 100%\",\r\n                  }}\r\n                  animate={{\r\n                    backgroundPosition: [\"0% 0%\", \"100% 0%\", \"0% 0%\"],\r\n                  }}\r\n                  transition={{\r\n                    duration: 5,\r\n                    ease: \"linear\",\r\n                    repeat: Infinity,\r\n                  }}\r\n                >\r\n                  CEPOKA BEAUTY HUB\r\n                  <motion.div\r\n                    className=\"absolute -bottom-1 left-0 h-[2px] rounded-full\"\r\n                    style={{\r\n                      backgroundImage: \"linear-gradient(90deg, #1E90FF, #FF69B4)\",\r\n                      backgroundSize: \"200% 100%\",\r\n                    }}\r\n                    animate={{\r\n                      backgroundPosition: [\"0% 0%\", \"100% 0%\", \"0% 0%\"],\r\n                      width: [\"0%\", \"100%\"],\r\n                    }}\r\n                    transition={{\r\n                      backgroundPosition: {\r\n                        duration: 5,\r\n                        ease: \"linear\",\r\n                        repeat: Infinity,\r\n                      },\r\n                      width: {\r\n                        duration: 1,\r\n                        delay: 0.5,\r\n                        ease: \"easeOut\",\r\n                      },\r\n                    }}\r\n                  />\r\n                </motion.span>\r\n              </div>\r\n            </Link>\r\n\r\n            <div className=\"flex items-center space-x-8\">\r\n              {/* Nav items - adjusted spacing */}\r\n              <motion.ul\r\n                animate={{ opacity: showSearch ? 0 : 1 }}\r\n                transition={{ duration: 0.2 }}\r\n                className={`list-none hidden md:flex items-center space-x-8 font-medium ${showSearch ? 'invisible' : 'visible'}`}\r\n              >\r\n                {[\"Home\", \"Shop\", \"Contact\", \"About\"].map((link) => (\r\n                  <li\r\n                    key={link}\r\n                    className={`relative cursor-pointer flex flex-col items-center ${activeLink === link\r\n                      ? \"text-[#1E90FF]\"\r\n                      : \"text-white/90 hover:text-white transition-colors duration-200\"\r\n                      }`}\r\n                    onClick={() => handleNavClick(link)}\r\n                  >\r\n                    <div>{link}</div>\r\n                    {activeLink === link && (\r\n                      <div className=\"absolute top-8 flex space-x-1\">\r\n                        <motion.div\r\n                          className=\"bg-[#1E90FF] w-[4px] h-[4px] rounded-full\"\r\n                          initial={{ scale: 0 }}\r\n                          animate={{ scale: 1 }}\r\n                          transition={{ duration: 0.2 }}\r\n                        />\r\n                        <motion.div\r\n                          className=\"bg-[#FF69B4] w-[4px] h-[4px] rounded-full\"\r\n                          initial={{ scale: 0 }}\r\n                          animate={{ scale: 1 }}\r\n                          transition={{ duration: 0.2, delay: 0.1 }}\r\n                        />\r\n                        <motion.div\r\n                          className=\"bg-[#1E90FF] w-[4px] h-[4px] rounded-full\"\r\n                          initial={{ scale: 0 }}\r\n                          animate={{ scale: 1 }}\r\n                          transition={{ duration: 0.2, delay: 0.2 }}\r\n                        />\r\n                        <motion.div\r\n                          className=\"bg-[#FF69B4] w-[4px] h-[4px] rounded-full\"\r\n                          initial={{ scale: 0 }}\r\n                          animate={{ scale: 1 }}\r\n                          transition={{ duration: 0.2, delay: 0.3 }}\r\n                        />\r\n                      </div>\r\n                    )}\r\n                  </li>\r\n                ))}\r\n              </motion.ul>\r\n\r\n              {/* Search container - adjusted sizing */}\r\n              <div className=\"relative flex items-center z-[101]\">\r\n                <button\r\n                  type=\"button\"\r\n                  className=\"rounded-full p-3 cursor-pointer hover:bg-white/10 hidden md:flex items-center justify-center relative z-[102]\"\r\n                  onClick={() => setShowSearch(!showSearch)}\r\n                >\r\n                  <Search className=\"w-4 h-4 text-white\" />\r\n                </button>\r\n\r\n                {/* Inline search bar */}\r\n                {showSearch && (\r\n                  <motion.form\r\n                    initial={{ opacity: 0, width: 0 }}\r\n                    animate={{ opacity: 1, width: \"240px\" }}\r\n                    exit={{ opacity: 0, width: 0 }}\r\n                    onSubmit={handleSearch}\r\n                    className=\"absolute right-12 top-1/2 -translate-y-1/2\"\r\n                  >\r\n                    <div className=\"relative\">\r\n                      <input\r\n                        type=\"text\"\r\n                        value={searchQuery}\r\n                        onChange={(e) => setSearchQuery(e.target.value)}\r\n                        className=\"w-full px-4 py-2 rounded-full bg-white/10 text-white font-light focus:outline-none text-left placeholder-white/60\"\r\n                        placeholder=\"Search products...\"\r\n                        autoFocus\r\n                      />\r\n                    </div>\r\n                  </motion.form>\r\n                )}\r\n              </div>\r\n\r\n              {/* Mobile menu button with higher z-index */}\r\n              <div className=\"md:hidden flex items-center\">\r\n                {!isMenuOpen && (\r\n                  <button\r\n                    id=\"hamburger-button\"\r\n                    className=\"text-white focus:outline-none z-[120] pr-4\" // Added padding-right\r\n                    onClick={(e) => {\r\n                      e.stopPropagation();\r\n                      setIsMenuOpen(true);\r\n                    }}\r\n                  >\r\n                    <svg\r\n                      className=\"w-6 h-6\"\r\n                      fill=\"none\"\r\n                      stroke=\"currentColor\"\r\n                      viewBox=\"0 0 24 24\"\r\n                      xmlns=\"http://www.w3.org/2000/svg\"\r\n                    >\r\n                      <path\r\n                        strokeLinecap=\"round\"\r\n                        strokeLinejoin=\"round\"\r\n                        strokeWidth=\"2\"\r\n                        d=\"M4 6h16M4 12h16m-7 6h7\"\r\n                      />\r\n                    </svg>\r\n                  </button>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Updated Mobile Menu with proper container */}\r\n          <motion.div\r\n            id=\"mobile-menu-container\"\r\n            initial=\"closed\"\r\n            animate={isMenuOpen ? \"open\" : \"closed\"}\r\n            variants={{\r\n              open: {\r\n                opacity: 1,\r\n                y: 0,\r\n                display: \"block\",\r\n                transition: {\r\n                  duration: 0.3,\r\n                  ease: [0.4, 0, 0.2, 1],\r\n                  staggerChildren: 0.1\r\n                }\r\n              },\r\n              closed: {\r\n                opacity: 0,\r\n                y: \"-100%\",\r\n                transitionEnd: {\r\n                  display: \"none\"\r\n                },\r\n                transition: {\r\n                  duration: 0.3,\r\n                  ease: [0.4, 0, 0.2, 1],\r\n                  staggerChildren: 0.05,\r\n                  staggerDirection: -1\r\n                }\r\n              }\r\n            }}\r\n            className=\"fixed top-0 left-0 w-full bg-[#11111180] backdrop-blur-[12px] z-[150] md:hidden h-full\"\r\n            onClick={(e) => {\r\n              e.stopPropagation();\r\n              setIsMenuOpen(false);\r\n            }}\r\n          >\r\n            {/* Mobile Header with extended background */}\r\n            <div className=\"bg-[#11111180] backdrop-blur-[12px] pb-8\">\r\n              {/* Header section */}\r\n              <div className=\"h-[90px] relative\">\r\n                <div className=\"absolute -bottom-10 left-0 w-full h-[1px] bg-black/10 z-[160]\"></div>\r\n                <div className=\"container mx-auto relative\">\r\n                  <div className=\"flex justify-between items-center relative z-[200] pt-6 px-8\">\r\n                    <Link href={\"/\"} className=\"relative z-[200] flex items-center gap-3\" onClick={(e) => e.stopPropagation()}>\r\n                      <div className=\"bg-transparent p-1 rounded-full\">\r\n                        <Image src=\"/logo.png\" alt=\"Logo\" width={40} height={50} />\r\n                      </div>\r\n                      <div>\r\n                        <motion.span\r\n                          className=\"text-base font-bold bg-clip-text text-transparent inline-block relative\"\r\n                          style={{\r\n                            backgroundImage: \"linear-gradient(90deg, #1E90FF, #FF69B4)\",\r\n                            backgroundSize: \"200% 100%\",\r\n                          }}\r\n                          animate={{\r\n                            backgroundPosition: [\"0% 0%\", \"100% 0%\", \"0% 0%\"],\r\n                          }}\r\n                          transition={{\r\n                            duration: 5,\r\n                            ease: \"linear\",\r\n                            repeat: Infinity,\r\n                          }}\r\n                        >\r\n                          CEPOKA BEAUTY HUB\r\n                          <motion.div\r\n                            className=\"absolute -bottom-1 left-0 h-[2px] rounded-full\"\r\n                            style={{\r\n                              backgroundImage: \"linear-gradient(90deg, #1E90FF, #FF69B4)\",\r\n                              backgroundSize: \"200% 100%\",\r\n                            }}\r\n                            animate={{\r\n                              backgroundPosition: [\"0% 0%\", \"100% 0%\", \"0% 0%\"],\r\n                              width: [\"0%\", \"100%\"],\r\n                            }}\r\n                            transition={{\r\n                              backgroundPosition: {\r\n                                duration: 5,\r\n                                ease: \"linear\",\r\n                                repeat: Infinity,\r\n                              },\r\n                              width: {\r\n                                duration: 1,\r\n                                delay: 0.5,\r\n                                ease: \"easeOut\",\r\n                              },\r\n                            }}\r\n                          />\r\n                        </motion.span>\r\n                      </div>\r\n                    </Link>\r\n\r\n                    <motion.button\r\n                      className=\"text-white bg-black/30 rounded-full p-2 relative z-[170] cursor-pointer hover:text-gray-300 hover:scale-110 transition-all duration-200\"\r\n                      onClick={(e) => {\r\n                        e.stopPropagation();\r\n                        setIsMenuOpen(false);\r\n                      }}\r\n                      whileHover={{ scale: 1.1 }}\r\n                      whileTap={{ scale: 0.9 }}\r\n                      aria-label=\"Close menu\"\r\n                    >\r\n                      <svg\r\n                        className=\"w-6 h-6\"\r\n                        fill=\"none\"\r\n                        stroke=\"currentColor\"\r\n                        viewBox=\"0 0 24 24\"\r\n                        xmlns=\"http://www.w3.org/2000/svg\"\r\n                      >\r\n                        <path\r\n                          strokeLinecap=\"round\"\r\n                          strokeLinejoin=\"round\"\r\n                          strokeWidth=\"2\"\r\n                          d=\"M6 18L18 6M6 6l12 12\"\r\n                        />\r\n                      </svg>\r\n                    </motion.button>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Mobile Menu Content - now inside the same background container */}\r\n              <div\r\n                className=\"pt-16 pb-8 px-8 flex flex-col items-center gap-8\"\r\n                onClick={(e) => e.stopPropagation()}\r\n              >\r\n                {/* Updated Mobile Search Form */}\r\n                <motion.form\r\n                  id=\"mobile-search-form\"\r\n                  variants={menuItemVariants}\r\n                  custom={0}\r\n                  className=\"relative w-full max-w-[280px]\"\r\n                  onSubmit={handleSearch}\r\n                  onClick={(e) => e.stopPropagation()}\r\n                >\r\n                  <input\r\n                    type=\"text\"\r\n                    value={searchQuery}\r\n                    onChange={(e) => setSearchQuery(e.target.value)}\r\n                    className=\"w-full px-6 py-3 rounded-full bg-white/10 text-white font-light focus:outline-none focus:ring-2 focus:ring-[#1E90FF] transition-all text-left placeholder-white/60\"\r\n                    placeholder=\"Search products...\"\r\n                  />\r\n                  <button\r\n                    type=\"submit\"\r\n                    className=\"absolute right-4 top-1/2 transform -translate-y-1/2 p-2\"\r\n                  >\r\n                    <Search className=\"w-4 h-4 text-white\" />\r\n                  </button>\r\n                </motion.form>\r\n\r\n                {/* Navigation Links */}\r\n                <div className=\"flex flex-col items-center gap-8 w-full\">\r\n                  {[\"Home\", \"Shop\", \"Contact\", \"About\"].map((link, i) => (\r\n                    <motion.div\r\n                      key={link}\r\n                      variants={menuItemVariants}\r\n                      custom={i + 1}\r\n                      className=\"relative flex flex-col items-center\"\r\n                    >\r\n                      <motion.button\r\n                        onClick={() => handleNavClick(link)}\r\n                        className={`text-center text-lg font-medium py-2 px-4 ${activeLink === link ? \"text-[#FF69B4]\" : \"text-white\"}`}\r\n                        whileHover={{ scale: 1.05 }}\r\n                        whileTap={{ scale: 0.95 }}\r\n                      >\r\n                        {link}\r\n                      </motion.button>\r\n                      {activeLink === link && (\r\n                        <div className=\"absolute -bottom-2 flex space-x-1\">\r\n                          <motion.div\r\n                            className=\"bg-[#1E90FF] w-[4px] h-[4px] rounded-full\"\r\n                            initial={{ scale: 0 }}\r\n                            animate={{ scale: 1 }}\r\n                            transition={{ duration: 0.2 }}\r\n                          />\r\n                          <motion.div\r\n                            className=\"bg-[#FF69B4] w-[4px] h-[4px] rounded-full\"\r\n                            initial={{ scale: 0 }}\r\n                            animate={{ scale: 1 }}\r\n                            transition={{ duration: 0.2, delay: 0.1 }}\r\n                          />\r\n                          <motion.div\r\n                            className=\"bg-[#1E90FF] w-[4px] h-[4px] rounded-full\"\r\n                            initial={{ scale: 0 }}\r\n                            animate={{ scale: 1 }}\r\n                            transition={{ duration: 0.2, delay: 0.2 }}\r\n                          />\r\n                          <motion.div\r\n                            className=\"bg-[#FF69B4] w-[4px] h-[4px] rounded-full\"\r\n                            initial={{ scale: 0 }}\r\n                            animate={{ scale: 1 }}\r\n                            transition={{ duration: 0.2, delay: 0.3 }}\r\n                          />\r\n                        </div>\r\n                      )}\r\n                    </motion.div>\r\n                  ))}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </motion.div>\r\n        </div>\r\n      </nav>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default Nav;\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;AAJA;AAKA;AARA;;;;;;;;;AAUA,MAAM,MAAM;IACV,MAAM,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,2IAAA,CAAA,gBAAa,AAAD;IAClD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD,KAAK,wBAAwB;IACxD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa;IACf,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,wCAAiD;;QAEjD,MAAM;IAkBR,GAAG;QAAC;QAAU;KAAU;IAExB,8DAA8D;IAC9D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,aAAa,SAAS;YACxB,cAAc;QAChB,OAAO,IAAI,aAAa,KAAK;YAC3B,cAAc;QAChB;IACF,GAAG;QAAC;QAAU;KAAc;IAE5B,MAAM,kBAAkB,CAAC;QACvB,wCAAiD;;QACjD,MAAM;IAIR;IAEA,MAAM,iBAAiB,CAAC;QACtB,cAAc;QACd,cAAc;QAEd,oCAAoC;QACpC,IAAI,YAAY,SAAS,UAAU,CAAC,WAAW;YAC7C,QAAQ,GAAG,CAAC;YACZ;QACF;QAEA,IAAI,aAAa,KAAK;YACpB,OAAQ,KAAK,WAAW;gBACtB,KAAK;oBACH,gBAAgB;oBAChB;gBACF,KAAK;oBACH,gBAAgB;oBAChB;gBACF,KAAK;oBACH,OAAO,QAAQ,CAAC;wBAAE,KAAK;wBAAG,UAAU;oBAAS;oBAC7C;gBACF,KAAK;oBACH,OAAO,IAAI,CAAC;oBACZ;YACJ;QACF,OAAO;YACL,IAAI,KAAK,WAAW,OAAO,QAAQ;gBACjC,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,OAAO,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,WAAW,IAAI;YACtC;QACF;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,GAAG;QACH,IAAI,YAAY,IAAI,IAAI;YACtB,OAAO,IAAI,CAAC,CAAC,aAAa,EAAE,mBAAmB,YAAY,IAAI,KAAK;QACtE;QACA,cAAc;QACd,eAAe;IACjB;IAEA,MAAM,mBAAmB;QACvB,QAAQ;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;QAC7B,MAAM,CAAC,IAAc,CAAC;gBACpB,SAAS;gBACT,GAAG;gBACH,YAAY;oBACV,OAAO,IAAI;oBACX,UAAU;oBACV,MAAM;wBAAC;wBAAK;wBAAG;wBAAK;qBAAE;gBACxB;YACF,CAAC;IACH;IAEA,mDAAmD;IACnD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,kBAAkB,CAAC;YACvB,IAAI,CAAC,YAAY;YAEjB,MAAM,SAAS,MAAM,MAAM;YAC3B,MAAM,aAAa,SAAS,cAAc,CAAC;YAC3C,MAAM,kBAAkB,SAAS,cAAc,CAAC;YAChD,MAAM,aAAa,SAAS,cAAc,CAAC;YAE3C,sDAAsD;YACtD,MAAM,gBAAgB,YAAY,SAAS;YAC3C,MAAM,kBAAkB,iBAAiB,SAAS;YAClD,MAAM,kBAAkB,YAAY,SAAS;YAC7C,MAAM,mBAAmB,OAAO,SAAS,CAAC,QAAQ,CAAC;YAEnD,oEAAoE;YACpE,IAAI,AAAC,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,mBAAoB,kBAAkB;gBAChF,cAAc;YAChB;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;IACzD,GAAG;QAAC;KAAW;IAEf,qBACE;;YAEG,4BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,MAAM;oBAAE,SAAS;gBAAE;gBACnB,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,WAAU;;;;;;0BAId,8OAAC;gBAAI,WAAW,CAAC,mLAAmL,EAAE,YAAY,kBAAkB,qBAAqB;0BACvP,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAW,CAAC,mDAAmD,EAAE,aAAa,cAAc,cAAc,gCAAgC,CAAC;;8CAE9I,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAM;oCAAK,WAAU;;sDACzB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;gDAAC,KAAI;gDAAY,KAAI;gDAAO,OAAO;gDAAI,QAAQ;;;;;;;;;;;sDAEvD,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;gDACV,WAAU;gDACV,OAAO;oDACL,iBAAiB;oDACjB,gBAAgB;gDAClB;gDACA,SAAS;oDACP,oBAAoB;wDAAC;wDAAS;wDAAW;qDAAQ;gDACnD;gDACA,YAAY;oDACV,UAAU;oDACV,MAAM;oDACN,QAAQ;gDACV;;oDACD;kEAEC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wDACT,WAAU;wDACV,OAAO;4DACL,iBAAiB;4DACjB,gBAAgB;wDAClB;wDACA,SAAS;4DACP,oBAAoB;gEAAC;gEAAS;gEAAW;6DAAQ;4DACjD,OAAO;gEAAC;gEAAM;6DAAO;wDACvB;wDACA,YAAY;4DACV,oBAAoB;gEAClB,UAAU;gEACV,MAAM;gEACN,QAAQ;4DACV;4DACA,OAAO;gEACL,UAAU;gEACV,OAAO;gEACP,MAAM;4DACR;wDACF;;;;;;;;;;;;;;;;;;;;;;;8CAMR,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;4CACR,SAAS;gDAAE,SAAS,aAAa,IAAI;4CAAE;4CACvC,YAAY;gDAAE,UAAU;4CAAI;4CAC5B,WAAW,CAAC,4DAA4D,EAAE,aAAa,cAAc,WAAW;sDAE/G;gDAAC;gDAAQ;gDAAQ;gDAAW;6CAAQ,CAAC,GAAG,CAAC,CAAC,qBACzC,8OAAC;oDAEC,WAAW,CAAC,mDAAmD,EAAE,eAAe,OAC5E,mBACA,iEACA;oDACJ,SAAS,IAAM,eAAe;;sEAE9B,8OAAC;sEAAK;;;;;;wDACL,eAAe,sBACd,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oEACT,WAAU;oEACV,SAAS;wEAAE,OAAO;oEAAE;oEACpB,SAAS;wEAAE,OAAO;oEAAE;oEACpB,YAAY;wEAAE,UAAU;oEAAI;;;;;;8EAE9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oEACT,WAAU;oEACV,SAAS;wEAAE,OAAO;oEAAE;oEACpB,SAAS;wEAAE,OAAO;oEAAE;oEACpB,YAAY;wEAAE,UAAU;wEAAK,OAAO;oEAAI;;;;;;8EAE1C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oEACT,WAAU;oEACV,SAAS;wEAAE,OAAO;oEAAE;oEACpB,SAAS;wEAAE,OAAO;oEAAE;oEACpB,YAAY;wEAAE,UAAU;wEAAK,OAAO;oEAAI;;;;;;8EAE1C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oEACT,WAAU;oEACV,SAAS;wEAAE,OAAO;oEAAE;oEACpB,SAAS;wEAAE,OAAO;oEAAE;oEACpB,YAAY;wEAAE,UAAU;wEAAK,OAAO;oEAAI;;;;;;;;;;;;;mDAhCzC;;;;;;;;;;sDAyCX,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,MAAK;oDACL,WAAU;oDACV,SAAS,IAAM,cAAc,CAAC;8DAE9B,cAAA,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;gDAInB,4BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;oDACV,SAAS;wDAAE,SAAS;wDAAG,OAAO;oDAAE;oDAChC,SAAS;wDAAE,SAAS;wDAAG,OAAO;oDAAQ;oDACtC,MAAM;wDAAE,SAAS;wDAAG,OAAO;oDAAE;oDAC7B,UAAU;oDACV,WAAU;8DAEV,cAAA,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DACC,MAAK;4DACL,OAAO;4DACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4DAC9C,WAAU;4DACV,aAAY;4DACZ,SAAS;;;;;;;;;;;;;;;;;;;;;;sDAQnB,8OAAC;4CAAI,WAAU;sDACZ,CAAC,4BACA,8OAAC;gDACC,IAAG;gDACH,WAAU,6CAA6C,sBAAsB;;gDAC7E,SAAS,CAAC;oDACR,EAAE,eAAe;oDACjB,cAAc;gDAChB;0DAEA,cAAA,8OAAC;oDACC,WAAU;oDACV,MAAK;oDACL,QAAO;oDACP,SAAQ;oDACR,OAAM;8DAEN,cAAA,8OAAC;wDACC,eAAc;wDACd,gBAAe;wDACf,aAAY;wDACZ,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAUhB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,IAAG;4BACH,SAAQ;4BACR,SAAS,aAAa,SAAS;4BAC/B,UAAU;gCACR,MAAM;oCACJ,SAAS;oCACT,GAAG;oCACH,SAAS;oCACT,YAAY;wCACV,UAAU;wCACV,MAAM;4CAAC;4CAAK;4CAAG;4CAAK;yCAAE;wCACtB,iBAAiB;oCACnB;gCACF;gCACA,QAAQ;oCACN,SAAS;oCACT,GAAG;oCACH,eAAe;wCACb,SAAS;oCACX;oCACA,YAAY;wCACV,UAAU;wCACV,MAAM;4CAAC;4CAAK;4CAAG;4CAAK;yCAAE;wCACtB,iBAAiB;wCACjB,kBAAkB,CAAC;oCACrB;gCACF;4BACF;4BACA,WAAU;4BACV,SAAS,CAAC;gCACR,EAAE,eAAe;gCACjB,cAAc;4BAChB;sCAGA,cAAA,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,4JAAA,CAAA,UAAI;4DAAC,MAAM;4DAAK,WAAU;4DAA2C,SAAS,CAAC,IAAM,EAAE,eAAe;;8EACrG,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;wEAAC,KAAI;wEAAY,KAAI;wEAAO,OAAO;wEAAI,QAAQ;;;;;;;;;;;8EAEvD,8OAAC;8EACC,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;wEACV,WAAU;wEACV,OAAO;4EACL,iBAAiB;4EACjB,gBAAgB;wEAClB;wEACA,SAAS;4EACP,oBAAoB;gFAAC;gFAAS;gFAAW;6EAAQ;wEACnD;wEACA,YAAY;4EACV,UAAU;4EACV,MAAM;4EACN,QAAQ;wEACV;;4EACD;0FAEC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gFACT,WAAU;gFACV,OAAO;oFACL,iBAAiB;oFACjB,gBAAgB;gFAClB;gFACA,SAAS;oFACP,oBAAoB;wFAAC;wFAAS;wFAAW;qFAAQ;oFACjD,OAAO;wFAAC;wFAAM;qFAAO;gFACvB;gFACA,YAAY;oFACV,oBAAoB;wFAClB,UAAU;wFACV,MAAM;wFACN,QAAQ;oFACV;oFACA,OAAO;wFACL,UAAU;wFACV,OAAO;wFACP,MAAM;oFACR;gFACF;;;;;;;;;;;;;;;;;;;;;;;sEAMR,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4DACZ,WAAU;4DACV,SAAS,CAAC;gEACR,EAAE,eAAe;gEACjB,cAAc;4DAChB;4DACA,YAAY;gEAAE,OAAO;4DAAI;4DACzB,UAAU;gEAAE,OAAO;4DAAI;4DACvB,cAAW;sEAEX,cAAA,8OAAC;gEACC,WAAU;gEACV,MAAK;gEACL,QAAO;gEACP,SAAQ;gEACR,OAAM;0EAEN,cAAA,8OAAC;oEACC,eAAc;oEACd,gBAAe;oEACf,aAAY;oEACZ,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDASd,8OAAC;wCACC,WAAU;wCACV,SAAS,CAAC,IAAM,EAAE,eAAe;;0DAGjC,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;gDACV,IAAG;gDACH,UAAU;gDACV,QAAQ;gDACR,WAAU;gDACV,UAAU;gDACV,SAAS,CAAC,IAAM,EAAE,eAAe;;kEAEjC,8OAAC;wDACC,MAAK;wDACL,OAAO;wDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wDAC9C,WAAU;wDACV,aAAY;;;;;;kEAEd,8OAAC;wDACC,MAAK;wDACL,WAAU;kEAEV,cAAA,8OAAC,sMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAKtB,8OAAC;gDAAI,WAAU;0DACZ;oDAAC;oDAAQ;oDAAQ;oDAAW;iDAAQ,CAAC,GAAG,CAAC,CAAC,MAAM,kBAC/C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wDAET,UAAU;wDACV,QAAQ,IAAI;wDACZ,WAAU;;0EAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gEACZ,SAAS,IAAM,eAAe;gEAC9B,WAAW,CAAC,0CAA0C,EAAE,eAAe,OAAO,mBAAmB,cAAc;gEAC/G,YAAY;oEAAE,OAAO;gEAAK;gEAC1B,UAAU;oEAAE,OAAO;gEAAK;0EAEvB;;;;;;4DAEF,eAAe,sBACd,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wEACT,WAAU;wEACV,SAAS;4EAAE,OAAO;wEAAE;wEACpB,SAAS;4EAAE,OAAO;wEAAE;wEACpB,YAAY;4EAAE,UAAU;wEAAI;;;;;;kFAE9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wEACT,WAAU;wEACV,SAAS;4EAAE,OAAO;wEAAE;wEACpB,SAAS;4EAAE,OAAO;wEAAE;wEACpB,YAAY;4EAAE,UAAU;4EAAK,OAAO;wEAAI;;;;;;kFAE1C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wEACT,WAAU;wEACV,SAAS;4EAAE,OAAO;wEAAE;wEACpB,SAAS;4EAAE,OAAO;wEAAE;wEACpB,YAAY;4EAAE,UAAU;4EAAK,OAAO;wEAAI;;;;;;kFAE1C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wEACT,WAAU;wEACV,SAAS;4EAAE,OAAO;wEAAE;wEACpB,SAAS;4EAAE,OAAO;wEAAE;wEACpB,YAAY;4EAAE,UAAU;4EAAK,OAAO;wEAAI;;;;;;;;;;;;;uDArCzC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmD3B;uCAEe"}}, {"offset": {"line": 973, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 979, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/Code/Web%20Projects/Client%20Projects/Cepoka%20Website/cepoka/src/app/pwa.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\n\nexport default function PWA() {\n  useEffect(() => {\n    if ('serviceWorker' in navigator && typeof window !== 'undefined') {\n      window.addEventListener('load', function() {\n        navigator.serviceWorker.register('/sw.js').then(\n          function(registration) {\n            console.log('Service Worker registration successful with scope: ', registration.scope);\n          },\n          function(err) {\n            console.log('Service Worker registration failed: ', err);\n          }\n        );\n      });\n    }\n  }, []);\n\n  return null;\n}\n"], "names": [], "mappings": ";;;AAEA;AAFA;;AAIe,SAAS;IACtB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,mBAAmB,aAAa,gBAAkB,aAAa;;QAWnE;IACF,GAAG,EAAE;IAEL,OAAO;AACT"}}, {"offset": {"line": 993, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 999, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/Code/Web%20Projects/Client%20Projects/Cepoka%20Website/cepoka/src/app/Components/InstallPrompt.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\n// Define types for the beforeinstallprompt event\ninterface BeforeInstallPromptEvent extends Event {\n  readonly platforms: string[];\n  readonly userChoice: Promise<{\n    outcome: 'accepted' | 'dismissed';\n    platform: string;\n  }>;\n  prompt(): Promise<void>;\n}\n\n// Declare the event for TypeScript\ndeclare global {\n  interface WindowEventMap {\n    beforeinstallprompt: BeforeInstallPromptEvent;\n  }\n}\n\nexport default function InstallPrompt() {\n  const [showPrompt, setShowPrompt] = useState(false);\n  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null);\n  const [isIOS, setIsIOS] = useState(false);\n\n  useEffect(() => {\n    // Check if the app is already installed\n    const isStandalone = window.matchMedia('(display-mode: standalone)').matches;\n\n    // Check if it's iOS\n    const isIOSDevice = /iPad|iPhone|iPod/.test(navigator.userAgent) && !(window as { MSStream?: unknown }).MSStream;\n    setIsIOS(isIOSDevice);\n\n    if (!isStandalone) {\n      // Listen for the beforeinstallprompt event (works on Android/Chrome)\n      const handleBeforeInstallPrompt = (e: BeforeInstallPromptEvent) => {\n        // Prevent Chrome 67 and earlier from automatically showing the prompt\n        e.preventDefault();\n        // Stash the event so it can be triggered later\n        setDeferredPrompt(e);\n        // Show the install prompt\n        setShowPrompt(true);\n      };\n\n      window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);\n\n      return () => {\n        window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);\n      };\n    }\n\n    return undefined;\n  }, []);\n\n  const handleInstallClick = async () => {\n    if (!deferredPrompt) return;\n\n    // Show the install prompt\n    await deferredPrompt.prompt();\n\n    // Wait for the user to respond to the prompt\n    const choiceResult = await deferredPrompt.userChoice;\n\n    if (choiceResult.outcome === 'accepted') {\n      console.log('User accepted the install prompt');\n    } else {\n      console.log('User dismissed the install prompt');\n    }\n\n    // Clear the saved prompt since it can't be used again\n    setDeferredPrompt(null);\n    setShowPrompt(false);\n  };\n\n  const closePrompt = () => {\n    setShowPrompt(false);\n  };\n\n  if (!showPrompt) return null;\n\n  return (\n    <div className=\"fixed bottom-0 left-0 right-0 bg-white p-4 shadow-lg z-50 flex justify-between items-center\">\n      <div>\n        <p className=\"font-medium\">Install Cepoka Beauty Hub</p>\n        <p className=\"text-sm text-gray-600\">\n          {isIOS\n            ? 'Tap the share button and select \"Add to Home Screen\"'\n            : 'Install this app on your device for quick access'}\n        </p>\n      </div>\n      <div className=\"flex gap-2\">\n        {!isIOS && (\n          <button\n            onClick={handleInstallClick}\n            className=\"bg-black text-white px-4 py-2 rounded-lg\"\n          >\n            Install\n          </button>\n        )}\n        <button\n          onClick={closePrompt}\n          className=\"border border-gray-300 px-4 py-2 rounded-lg\"\n        >\n          Not now\n        </button>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAqBe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmC;IACtF,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,wCAAwC;QACxC,MAAM,eAAe,OAAO,UAAU,CAAC,8BAA8B,OAAO;QAE5E,oBAAoB;QACpB,MAAM,cAAc,mBAAmB,IAAI,CAAC,UAAU,SAAS,KAAK,CAAC,AAAC,OAAkC,QAAQ;QAChH,SAAS;QAET,IAAI,CAAC,cAAc;YACjB,qEAAqE;YACrE,MAAM,4BAA4B,CAAC;gBACjC,sEAAsE;gBACtE,EAAE,cAAc;gBAChB,+CAA+C;gBAC/C,kBAAkB;gBAClB,0BAA0B;gBAC1B,cAAc;YAChB;YAEA,OAAO,gBAAgB,CAAC,uBAAuB;YAE/C,OAAO;gBACL,OAAO,mBAAmB,CAAC,uBAAuB;YACpD;QACF;QAEA,OAAO;IACT,GAAG,EAAE;IAEL,MAAM,qBAAqB;QACzB,IAAI,CAAC,gBAAgB;QAErB,0BAA0B;QAC1B,MAAM,eAAe,MAAM;QAE3B,6CAA6C;QAC7C,MAAM,eAAe,MAAM,eAAe,UAAU;QAEpD,IAAI,aAAa,OAAO,KAAK,YAAY;YACvC,QAAQ,GAAG,CAAC;QACd,OAAO;YACL,QAAQ,GAAG,CAAC;QACd;QAEA,sDAAsD;QACtD,kBAAkB;QAClB,cAAc;IAChB;IAEA,MAAM,cAAc;QAClB,cAAc;IAChB;IAEA,IAAI,CAAC,YAAY,OAAO;IAExB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;;kCACC,8OAAC;wBAAE,WAAU;kCAAc;;;;;;kCAC3B,8OAAC;wBAAE,WAAU;kCACV,QACG,yDACA;;;;;;;;;;;;0BAGR,8OAAC;gBAAI,WAAU;;oBACZ,CAAC,uBACA,8OAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;kCAIH,8OAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;;;;;;;AAMT"}}, {"offset": {"line": 1114, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}