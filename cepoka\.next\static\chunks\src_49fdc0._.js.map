{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/Code/Web%20Projects/Client%20Projects/Cepoka%20Website/cepoka/src/lib/appwrite.ts"], "sourcesContent": ["import { Client, Account, Databases, Storage } from \"appwrite\";\r\n\r\n// Initialize the Appwrite client\r\nconst client = new Client()\r\n  .setEndpoint(\"https://cloud.appwrite.io/v1\")\r\n  .setProject(\"67d07dc9000bafdd5d81\"); // Confirmed correct project ID\r\n\r\nexport const account = new Account(client);\r\nexport const databases = new Databases(client);\r\nexport const storage = new Storage(client);\r\n\r\nexport const appwriteConfig = {\r\n  // Using the confirmed database ID\r\n  databaseId: \"6813eadb003e7d64f63c\",\r\n  productsCollectionId: \"6813eaf40036e52c29b1\",\r\n  categoriesCollectionId: \"6817640f000dd0b67c77\",\r\n  stockProductsCollectionId: \"681a651d001cc3de8395\",\r\n  stockMovementsCollectionId: \"681bddcc000204a3748d\",\r\n  storageId: \"6813ea36001624c1202a\",\r\n};\r\n\r\n// project id: 67d07d7b0010f39ec77d\r\n// database id: 67d8833d000778157021\r\n// collection id: 67d8835b002502c5d7ba\r\n// storage id: 67d8841a001213adf116\r\n"], "names": [], "mappings": ";;;;;;AAAA;;AAEA,iCAAiC;AACjC,MAAM,SAAS,IAAI,iJAAA,CAAA,SAAM,GACtB,WAAW,CAAC,gCACZ,UAAU,CAAC,yBAAyB,+BAA+B;AAE/D,MAAM,UAAU,IAAI,iJAAA,CAAA,UAAO,CAAC;AAC5B,MAAM,YAAY,IAAI,iJAAA,CAAA,YAAS,CAAC;AAChC,MAAM,UAAU,IAAI,iJAAA,CAAA,UAAO,CAAC;AAE5B,MAAM,iBAAiB;IAC5B,kCAAkC;IAClC,YAAY;IACZ,sBAAsB;IACtB,wBAAwB;IACxB,2BAA2B;IAC3B,4BAA4B;IAC5B,WAAW;AACb,GAEA,mCAAmC;CACnC,oCAAoC;CACpC,sCAAsC;CACtC,mCAAmC"}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 41, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/Code/Web%20Projects/Client%20Projects/Cepoka%20Website/cepoka/src/app/Components/SpinningLoader.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\n\ninterface SpinningLoaderProps {\n  size?: 'small' | 'medium' | 'large';\n  className?: string;\n  text?: string;\n}\n\nconst SpinningLoader: React.FC<SpinningLoaderProps> = ({ \n  size = 'medium', \n  className = '',\n  text\n}) => {\n  // Size mapping\n  const sizeMap = {\n    small: 'w-6 h-6 border-2',\n    medium: 'w-10 h-10 border-3',\n    large: 'w-16 h-16 border-4',\n  };\n\n  const sizeClass = sizeMap[size];\n  \n  return (\n    <div className={`flex flex-col items-center justify-center ${className}`}>\n      <motion.div\n        className={`${sizeClass} rounded-full border-t-pink-500 border-r-blue-500 border-b-pink-500 border-l-blue-500`}\n        animate={{ rotate: 360 }}\n        transition={{\n          duration: 1.5,\n          repeat: Infinity,\n          ease: \"linear\"\n        }}\n        style={{ borderStyle: 'solid' }}\n      />\n      {text && (\n        <p className=\"mt-3 text-sm text-gray-600 font-medium\">{text}</p>\n      )}\n    </div>\n  );\n};\n\nexport default SpinningLoader;\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAWA,MAAM,iBAAgD,CAAC,EACrD,OAAO,QAAQ,EACf,YAAY,EAAE,EACd,IAAI,EACL;IACC,eAAe;IACf,MAAM,UAAU;QACd,OAAO;QACP,QAAQ;QACR,OAAO;IACT;IAEA,MAAM,YAAY,OAAO,CAAC,KAAK;IAE/B,qBACE,6LAAC;QAAI,WAAW,CAAC,0CAA0C,EAAE,WAAW;;0BACtE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAW,GAAG,UAAU,qFAAqF,CAAC;gBAC9G,SAAS;oBAAE,QAAQ;gBAAI;gBACvB,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,MAAM;gBACR;gBACA,OAAO;oBAAE,aAAa;gBAAQ;;;;;;YAE/B,sBACC,6LAAC;gBAAE,WAAU;0BAA0C;;;;;;;;;;;;AAI/D;KA/BM;uCAiCS"}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 106, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/Code/Web%20Projects/Client%20Projects/Cepoka%20Website/cepoka/src/app/admin/receipt-sender/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useRef, useEffect } from 'react';\r\nimport Script from 'next/script';\r\nimport Link from 'next/link';\r\nimport { storage, appwriteConfig, databases } from '@/src/lib/appwrite';\r\nimport { ID, Query } from 'appwrite';\r\nimport { motion } from 'framer-motion';\r\n\r\n// Define a fixed document ID for the receipt counter\r\nconst RECEIPT_COUNTER_ID = 'receipt-counter';\r\n// Use the dedicated Receipt ID collection\r\nconst RECEIPT_COLLECTION_ID = '681721d5001b6819df1b';\r\n// Use the PDF storage collection\r\nconst PDF_COLLECTION_ID = '6817243c001593c1b882';\r\n\r\ninterface Html2PdfOptions {\r\n    margin: number[];\r\n    filename: string;\r\n    image: { type: string; quality: number };\r\n    html2canvas: {\r\n        scale?: number;\r\n        useCORS?: boolean;\r\n        letterRendering?: boolean;\r\n        scrollY?: number;\r\n        windowWidth?: number;\r\n        windowHeight?: number;\r\n        onrendered?: (canvas: HTMLCanvasElement) => void;\r\n        allowTaint?: boolean;\r\n        foreignObjectRendering?: boolean;\r\n        // Add any other properties that might be used\r\n        [key: string]: unknown;\r\n    };\r\n    jsPDF: {\r\n        unit: string;\r\n        format: string | number[];\r\n        orientation: 'portrait' | 'landscape';\r\n        putOnlyUsedFonts?: boolean;\r\n        compress?: boolean;\r\n    };\r\n}\r\n\r\ninterface Html2PdfResult {\r\n    save(): Promise<void>;\r\n    from(element: HTMLElement): Html2PdfResult;\r\n    set(options: Html2PdfOptions): Html2PdfResult;\r\n    output(type: string): Promise<string | Blob>;\r\n    outputPdf?(type: string): Promise<Blob>; // Add optional outputPdf method\r\n}\r\n\r\ndeclare global {\r\n    interface Window {\r\n        html2pdf: {\r\n            (): Html2PdfResult;\r\n            set: (opt: Html2PdfOptions) => Html2PdfResult;\r\n            from: (element: HTMLElement) => Html2PdfResult;\r\n            save: () => Promise<void>;\r\n            output: (type: string) => Promise<string | Blob>;\r\n        };\r\n    }\r\n}\r\n// Link is used in the JSX\r\nimport SpinningLoader from '../../Components/SpinningLoader';\r\nimport Image from 'next/image';\r\nimport toast from 'react-hot-toast';\r\n\r\ninterface LineItem {\r\n    description: string;\r\n    quantity: number;\r\n    unitPrice: number;\r\n    total: number;\r\n}\r\n\r\ninterface ReceiptData {\r\n    customerName: string;\r\n    whatsapp: string;\r\n    items: LineItem[];\r\n    subtotal: number;\r\n    amountPaid: number;\r\n    balance: number;\r\n    receiptNumber: string;\r\n    date: string;\r\n    receiptIdNumber?: number; // The numeric part of the receipt ID\r\n}\r\n\r\nconst ReceiptSender = () => {\r\n    const [formData, setFormData] = useState<ReceiptData>({\r\n        customerName: '',\r\n        whatsapp: '',\r\n        items: [{ description: '', quantity: 1, unitPrice: 0, total: 0 }],\r\n        subtotal: 0,\r\n        amountPaid: 0,\r\n        balance: 0,\r\n        receiptNumber: '',\r\n        date: new Date().toISOString().split('T')[0],\r\n        receiptIdNumber: 0,\r\n    });\r\n    const [showPreview, setShowPreview] = useState(false);\r\n    const [isLoadingId, setIsLoadingId] = useState(false);\r\n    const [startingIdNumber, setStartingIdNumber] = useState<number | ''>('');\r\n    const [showIdSettings, setShowIdSettings] = useState(false);\r\n    const [showSizeModal, setShowSizeModal] = useState(false);\r\n    const [receiptSize, setReceiptSize] = useState<'big' | 'mini'>('big');\r\n    const receiptRef = useRef<HTMLDivElement>(null);\r\n\r\n    // Function to fetch the current receipt ID from Appwrite\r\n    const fetchReceiptId = async () => {\r\n        try {\r\n            setIsLoadingId(true);\r\n            console.log(\"=== RECEIPT ID FETCH START ===\");\r\n            console.log(\"Database ID:\", appwriteConfig.databaseId);\r\n            console.log(\"Collection ID:\", RECEIPT_COLLECTION_ID);\r\n            console.log(\"Document ID:\", RECEIPT_COUNTER_ID);\r\n\r\n            // Try to get the receipt counter document directly\r\n            try {\r\n                console.log(\"Attempting to get document with ID:\", RECEIPT_COUNTER_ID);\r\n                const counterDoc = await databases.getDocument(\r\n                    appwriteConfig.databaseId,\r\n                    RECEIPT_COLLECTION_ID,\r\n                    RECEIPT_COUNTER_ID\r\n                );\r\n\r\n                console.log(\"Found receipt counter document:\", counterDoc);\r\n                console.log(\"Raw receiptId value:\", counterDoc.receiptId);\r\n                console.log(\"receiptId type:\", typeof counterDoc.receiptId);\r\n\r\n                // Clear indication if receipt ID is coming through from Appwrite\r\n                console.log(\"✅ RECEIPT ID FROM APPWRITE:\", counterDoc.receiptId ? \"YES - Value: \" + counterDoc.receiptId : \"NO - Value is missing or null\");\r\n\r\n                // Convert string ID to number, fallback to 1000 if not a valid number\r\n                let currentId = counterDoc.receiptId ? parseInt(counterDoc.receiptId, 10) : 1000;\r\n                console.log(\"Parsed receiptId to number:\", currentId);\r\n\r\n                // Use 1000 as fallback if parsing results in NaN\r\n                currentId = isNaN(currentId) ? 1000 : currentId;\r\n                console.log(\"Final currentId after validation:\", currentId);\r\n\r\n                // Generate the receipt number with the CEP prefix\r\n                const newReceiptNumber = `CEP${currentId}`;\r\n                console.log(\"Generated receipt number:\", newReceiptNumber);\r\n\r\n                setFormData(prev => ({\r\n                    ...prev,\r\n                    receiptNumber: newReceiptNumber,\r\n                    receiptIdNumber: currentId\r\n                }));\r\n                console.log(\"Form data updated with receipt number\");\r\n                return;\r\n            } catch (getError) {\r\n                console.error('Error getting receipt counter document:', getError);\r\n                console.log(\"Error details:\", JSON.stringify(getError));\r\n                console.log(\"Will create a new receipt counter document\");\r\n            }\r\n\r\n            // If we get here, the document doesn't exist\r\n            // Create the document with a default value\r\n            try {\r\n                const defaultId = 1000;\r\n                console.log(\"Creating new receipt counter document with default ID:\", defaultId);\r\n                console.log(\"Document data to be created:\", {\r\n                    receiptId: defaultId.toString()\r\n                });\r\n\r\n                const createResponse = await databases.createDocument(\r\n                    appwriteConfig.databaseId,\r\n                    RECEIPT_COLLECTION_ID,\r\n                    RECEIPT_COUNTER_ID,\r\n                    {\r\n                        receiptId: defaultId.toString()\r\n                    }\r\n                );\r\n\r\n                console.log(\"Document creation successful:\", createResponse);\r\n                console.log(\"Created document ID:\", createResponse.$id);\r\n                console.log(\"Created document receiptId:\", createResponse.receiptId);\r\n\r\n                // Clear indication if receipt ID was successfully stored in Appwrite\r\n                console.log(\"✅ RECEIPT ID STORED IN APPWRITE:\", createResponse.receiptId ? \"YES - Value: \" + createResponse.receiptId : \"NO - Value is missing or null\");\r\n\r\n                // Set the receipt number with the default ID\r\n                setFormData(prev => ({\r\n                    ...prev,\r\n                    receiptNumber: `CEP${defaultId}`,\r\n                    receiptIdNumber: defaultId\r\n                }));\r\n                console.log(\"Form data updated with default receipt number:\", `CEP${defaultId}`);\r\n                return;\r\n            } catch (createError) {\r\n                console.error('Error creating receipt counter document:', createError);\r\n                console.log(\"Error details:\", JSON.stringify(createError, null, 2));\r\n                throw createError; // Re-throw to be caught by the outer catch\r\n            }\r\n        } catch (error) {\r\n            console.error('Error in receipt ID generation:', error);\r\n            console.log(\"Error type:\", typeof error);\r\n            console.log(\"Error details:\", JSON.stringify(error, null, 2));\r\n\r\n            // Fallback to timestamp-based ID if there's an error\r\n            const timestamp = new Date().getTime().toString().slice(-6);\r\n            console.log(\"Using fallback timestamp-based ID:\", timestamp);\r\n\r\n            setFormData(prev => ({\r\n                ...prev,\r\n                receiptNumber: `CEP${timestamp}`\r\n            }));\r\n            console.log(\"Form data updated with fallback receipt number:\", `CEP${timestamp}`);\r\n\r\n            toast.error('Using fallback receipt ID. Please check Appwrite setup.');\r\n        } finally {\r\n            setIsLoadingId(false);\r\n            console.log(\"=== RECEIPT ID FETCH COMPLETE ===\");\r\n        }\r\n    };\r\n\r\n    // Function to update the receipt ID in Appwrite\r\n    const updateReceiptId = async (newId: number) => {\r\n        try {\r\n            console.log(\"=== UPDATING RECEIPT ID ===\");\r\n            console.log(\"Attempting to update receipt ID to:\", newId);\r\n\r\n            // First check if the document exists\r\n            try {\r\n                const existingDoc = await databases.getDocument(\r\n                    appwriteConfig.databaseId,\r\n                    RECEIPT_COLLECTION_ID,\r\n                    RECEIPT_COUNTER_ID\r\n                );\r\n\r\n                console.log(\"Found existing receipt counter document:\", existingDoc);\r\n\r\n                // If it exists, update it\r\n                console.log(\"Updating document with new receiptId:\", newId.toString());\r\n                const updateResponse = await databases.updateDocument(\r\n                    appwriteConfig.databaseId,\r\n                    RECEIPT_COLLECTION_ID,\r\n                    RECEIPT_COUNTER_ID,\r\n                    {\r\n                        receiptId: newId.toString()\r\n                    }\r\n                );\r\n\r\n                console.log(\"Update successful:\", updateResponse);\r\n                console.log(\"✅ RECEIPT ID UPDATED IN APPWRITE:\", updateResponse.receiptId);\r\n                return true;\r\n            } catch (getError) {\r\n                console.error('Document not found, creating new one:', getError);\r\n\r\n                // If document doesn't exist, create it\r\n                console.log(\"Creating new receipt counter document with ID:\", newId.toString());\r\n                const createResponse = await databases.createDocument(\r\n                    appwriteConfig.databaseId,\r\n                    RECEIPT_COLLECTION_ID,\r\n                    RECEIPT_COUNTER_ID,\r\n                    {\r\n                        receiptId: newId.toString()\r\n                    }\r\n                );\r\n\r\n                console.log(\"Document creation successful:\", createResponse);\r\n                console.log(\"✅ RECEIPT ID CREATED IN APPWRITE:\", createResponse.receiptId);\r\n                return true;\r\n            }\r\n        } catch (error) {\r\n            console.error('Error updating receipt ID:', error);\r\n            console.log(\"Error details:\", JSON.stringify(error, null, 2));\r\n            toast.error('Failed to update receipt ID. Please try again.');\r\n            return false;\r\n        }\r\n    };\r\n\r\n    // Function to generate a new receipt ID\r\n    const generateNewReceiptId = async () => {\r\n        try {\r\n            setIsLoadingId(true);\r\n\r\n            let currentId = 1000;\r\n\r\n            // Try to get the current ID\r\n            console.log(\"=== GENERATE NEW RECEIPT ID ===\");\r\n            console.log(\"Attempting to get document with ID:\", RECEIPT_COUNTER_ID);\r\n\r\n            try {\r\n                const response = await databases.getDocument(\r\n                    appwriteConfig.databaseId,\r\n                    RECEIPT_COLLECTION_ID,\r\n                    RECEIPT_COUNTER_ID\r\n                );\r\n\r\n                console.log(\"getDocument response:\", response);\r\n                console.log(\"Raw receiptId from getDocument:\", response.receiptId);\r\n                console.log(\"receiptId type:\", typeof response.receiptId);\r\n\r\n                // Clear indication if receipt ID is coming through from Appwrite in generateNewReceiptId\r\n                console.log(\"✅ RECEIPT ID FROM APPWRITE (generateNewReceiptId):\",\r\n                    response.receiptId ? \"YES - Value: \" + response.receiptId : \"NO - Value is missing or null\");\r\n\r\n                // Get the current ID if document exists and convert from string to number\r\n                const idFromDb = response.receiptId ? parseInt(response.receiptId, 10) : 1000;\r\n                console.log(\"Parsed receiptId to number:\", idFromDb);\r\n\r\n                // Use 1000 as fallback if parsing results in NaN\r\n                currentId = isNaN(idFromDb) ? 1000 : idFromDb;\r\n                console.log(\"Final currentId after validation:\", currentId);\r\n            } catch (getError) {\r\n                console.error('Error getting receipt counter, using default:', getError);\r\n                console.log(\"Error details:\", JSON.stringify(getError, null, 2));\r\n                // If document doesn't exist, we'll use the default 1000\r\n            }\r\n\r\n            // Increment the ID\r\n            const newId = currentId + 1;\r\n            console.log(\"New ID after increment:\", newId);\r\n\r\n            // Update the ID in Appwrite\r\n            console.log(\"Updating receipt ID in Appwrite to:\", newId);\r\n            const updated = await updateReceiptId(newId);\r\n            console.log(\"Update result:\", updated);\r\n\r\n            if (updated) {\r\n                // Update the receipt number in the form\r\n                const newReceiptNumber = `CEP${newId}`;\r\n                console.log(\"Setting new receipt number:\", newReceiptNumber);\r\n\r\n                setFormData(prev => ({\r\n                    ...prev,\r\n                    receiptNumber: newReceiptNumber,\r\n                    receiptIdNumber: newId\r\n                }));\r\n                console.log(\"Form data updated with new receipt number\");\r\n\r\n                toast.success('Generated new receipt ID');\r\n            } else {\r\n                console.log(\"Failed to update receipt ID\");\r\n                toast.error('Failed to update receipt ID');\r\n            }\r\n        } catch (error) {\r\n            console.error('Error generating new receipt ID:', error);\r\n            console.log(\"Error details:\", JSON.stringify(error, null, 2));\r\n\r\n            // Generate a fallback ID based on timestamp\r\n            const timestamp = new Date().getTime().toString().slice(-6);\r\n            const fallbackId = `CEP${timestamp}`;\r\n            console.log(\"Using fallback timestamp-based ID:\", fallbackId);\r\n\r\n            setFormData(prev => ({\r\n                ...prev,\r\n                receiptNumber: fallbackId\r\n            }));\r\n            console.log(\"Form data updated with fallback receipt number\");\r\n\r\n            toast.error('Using fallback receipt ID due to error');\r\n        } finally {\r\n            setIsLoadingId(false);\r\n            console.log(\"=== GENERATE NEW RECEIPT ID COMPLETE ===\");\r\n        }\r\n    };\r\n\r\n    // Function to set a custom starting ID\r\n    const setCustomStartingId = async () => {\r\n        if (startingIdNumber === '' || isNaN(Number(startingIdNumber))) {\r\n            toast.error('Please enter a valid number');\r\n            return;\r\n        }\r\n\r\n        const newId = Number(startingIdNumber);\r\n        if (newId < 1000) {\r\n            toast.error('ID must be at least 1000');\r\n            return;\r\n        }\r\n\r\n        try {\r\n            setIsLoadingId(true);\r\n            console.log(\"=== SETTING CUSTOM RECEIPT ID ===\");\r\n            console.log(\"Setting custom receipt ID to:\", newId);\r\n\r\n            // Update the ID in Appwrite\r\n            const updated = await updateReceiptId(newId);\r\n            console.log(\"Update result:\", updated);\r\n\r\n            if (updated) {\r\n                // Update the receipt number in the form\r\n                const newReceiptNumber = `CEP${newId}`;\r\n                console.log(\"Setting new receipt number:\", newReceiptNumber);\r\n\r\n                setFormData(prev => ({\r\n                    ...prev,\r\n                    receiptNumber: newReceiptNumber,\r\n                    receiptIdNumber: newId\r\n                }));\r\n                console.log(\"Form data updated with custom receipt number\");\r\n\r\n                setStartingIdNumber('');\r\n                setShowIdSettings(false);\r\n                toast.success(`Receipt ID set to ${newId}`);\r\n            } else {\r\n                console.log(\"Failed to update receipt ID\");\r\n                toast.error('Failed to update receipt ID');\r\n            }\r\n        } catch (error) {\r\n            console.error('Error setting custom receipt ID:', error);\r\n            console.log(\"Error details:\", JSON.stringify(error, null, 2));\r\n\r\n            // Generate a fallback ID based on the custom number\r\n            const fallbackId = `CEP${newId}`;\r\n            console.log(\"Using fallback custom ID:\", fallbackId);\r\n\r\n            setFormData(prev => ({\r\n                ...prev,\r\n                receiptNumber: fallbackId,\r\n                receiptIdNumber: newId\r\n            }));\r\n            console.log(\"Form data updated with fallback custom receipt number\");\r\n\r\n            setStartingIdNumber('');\r\n            setShowIdSettings(false);\r\n            toast.error('Using custom ID locally only. Database update failed.');\r\n        } finally {\r\n            setIsLoadingId(false);\r\n            console.log(\"=== CUSTOM RECEIPT ID SETTING COMPLETE ===\");\r\n        }\r\n    };\r\n\r\n    // Function to initialize the receipt ID if it doesn't exist\r\n    const initializeReceiptId = async () => {\r\n        try {\r\n            console.log(\"=== INITIALIZING RECEIPT ID ===\");\r\n            console.log(\"Checking if receipt counter document exists...\");\r\n\r\n            try {\r\n                // Try to get the existing document\r\n                const existingDoc = await databases.getDocument(\r\n                    appwriteConfig.databaseId,\r\n                    RECEIPT_COLLECTION_ID,\r\n                    RECEIPT_COUNTER_ID\r\n                );\r\n\r\n                console.log(\"Receipt counter document exists:\", existingDoc);\r\n                console.log(\"Current receiptId:\", existingDoc.receiptId);\r\n\r\n                // If it exists, just fetch it\r\n                fetchReceiptId();\r\n            } catch {\r\n                console.log(\"Receipt counter document doesn't exist, creating it...\");\r\n\r\n                // Create the document with a default value\r\n                const defaultId = 1000;\r\n                await databases.createDocument(\r\n                    appwriteConfig.databaseId,\r\n                    RECEIPT_COLLECTION_ID,\r\n                    RECEIPT_COUNTER_ID,\r\n                    {\r\n                        receiptId: defaultId.toString()\r\n                    }\r\n                );\r\n\r\n                console.log(\"Receipt counter document created with default ID:\", defaultId);\r\n\r\n                // Now fetch it\r\n                fetchReceiptId();\r\n            }\r\n        } catch (error) {\r\n            console.error(\"Error initializing receipt ID:\", error);\r\n            // Still try to fetch in case there's a document\r\n            fetchReceiptId();\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        // Log Appwrite configuration for debugging\r\n        console.log(\"=== APPWRITE CONFIGURATION ===\");\r\n        console.log(\"Database ID:\", appwriteConfig.databaseId);\r\n        console.log(\"Products Collection ID:\", appwriteConfig.productsCollectionId);\r\n        console.log(\"Storage ID:\", appwriteConfig.storageId);\r\n        console.log(\"Receipt Counter ID:\", RECEIPT_COUNTER_ID);\r\n        console.log(\"Receipt Collection ID:\", RECEIPT_COLLECTION_ID);\r\n\r\n        // Initialize the receipt ID when the component mounts\r\n        initializeReceiptId();\r\n        // eslint-disable-next-line react-hooks/exhaustive-deps\r\n    }, []);\r\n\r\n    // Real-time calculations are now handled directly in the change handlers\r\n\r\n    const handleItemChange = (index: number, field: keyof LineItem, value: string | number) => {\r\n        // Create a copy of the items array\r\n        const newItems = [...formData.items];\r\n\r\n        // Update the specific field\r\n        newItems[index] = {\r\n            ...newItems[index],\r\n            [field]: value\r\n        };\r\n\r\n        // Calculate the total for this item immediately\r\n        if (field === 'quantity' || field === 'unitPrice') {\r\n            newItems[index].total = Number(newItems[index].quantity) * Number(newItems[index].unitPrice);\r\n        }\r\n\r\n        // Update the form data with the new items\r\n        setFormData(prev => {\r\n            // Calculate new subtotal based on all items\r\n            const subtotal = newItems.reduce((sum, item) => sum + item.total, 0);\r\n\r\n            // Calculate new balance based on the new subtotal and current amount paid\r\n            const balance = subtotal - prev.amountPaid;\r\n\r\n            // Return updated form data with new items, subtotal, and balance\r\n            return {\r\n                ...prev,\r\n                items: newItems,\r\n                subtotal,\r\n                balance\r\n            };\r\n        });\r\n    };\r\n\r\n    const addLineItem = () => {\r\n        setFormData(prev => {\r\n            // Add new empty item\r\n            const newItems = [...prev.items, { description: '', quantity: 1, unitPrice: 0, total: 0 }];\r\n\r\n            // No need to recalculate totals since the new item has total=0\r\n            // But we'll do it anyway for consistency\r\n            const subtotal = newItems.reduce((sum, item) => sum + item.total, 0);\r\n            const balance = subtotal - prev.amountPaid;\r\n\r\n            return {\r\n                ...prev,\r\n                items: newItems,\r\n                subtotal,\r\n                balance\r\n            };\r\n        });\r\n    };\r\n\r\n    const removeLineItem = (index: number) => {\r\n        if (formData.items.length > 1) {\r\n            const newItems = formData.items.filter((_, i) => i !== index);\r\n\r\n            // Update form data and recalculate totals\r\n            setFormData(prev => {\r\n                // Calculate new subtotal based on filtered items\r\n                const subtotal = newItems.reduce((sum, item) => sum + item.total, 0);\r\n\r\n                // Calculate new balance\r\n                const balance = subtotal - prev.amountPaid;\r\n\r\n                return {\r\n                    ...prev,\r\n                    items: newItems,\r\n                    subtotal,\r\n                    balance\r\n                };\r\n            });\r\n        }\r\n    };\r\n\r\n    const handleSubmit = (e: React.FormEvent) => {\r\n        e.preventDefault();\r\n        setShowSizeModal(true);\r\n    };\r\n\r\n    // Handle receipt size selection\r\n    const handleSizeSelection = (size: 'big' | 'mini') => {\r\n        setReceiptSize(size);\r\n        setShowSizeModal(false);\r\n        setShowPreview(true);\r\n    };\r\n\r\n    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\r\n        setFormData({\r\n            ...formData,\r\n            [e.target.name]: e.target.value\r\n        });\r\n    };\r\n\r\n    const downloadPDF = async () => {\r\n        try {\r\n            if (typeof window.html2pdf === 'function' && receiptRef.current) {\r\n                // Show loading toast with custom spinner\r\n                toast.loading(\r\n                    <div className=\"flex items-center gap-2\">\r\n                        <SpinningLoader size=\"small\" />\r\n                        <span>Generating PDF...</span>\r\n                    </div>\r\n                );\r\n\r\n                // Use a timeout to ensure the UI updates before PDF generation\r\n                await new Promise(resolve => setTimeout(resolve, 100));\r\n\r\n                const element = receiptRef.current;\r\n                const fileName = `${formData.receiptNumber}_${formData.customerName.replace(/\\s+/g, '_')}.pdf`;\r\n\r\n                console.log(\"=== GENERATING PDF ===\");\r\n                console.log(\"File name:\", fileName);\r\n\r\n                // Generate PDF directly from the visible receipt element\r\n                // This is more reliable than creating a clone\r\n                try {\r\n                    // Configure PDF options based on receipt size\r\n                    const opt: Html2PdfOptions = receiptSize === 'mini' ? {\r\n                        margin: [3, 3, 3, 3], // Envelope #9 compatible margins\r\n                        filename: fileName,\r\n                        image: { type: 'jpeg', quality: 0.95 },\r\n                        html2canvas: {\r\n                            scale: 2.5, // Higher scale for better text clarity on envelope size\r\n                            useCORS: true,\r\n                            logging: true,\r\n                            letterRendering: true,\r\n                            allowTaint: true,\r\n                            foreignObjectRendering: false,\r\n                            scrollY: 0,\r\n                            windowHeight: window.innerHeight * 2\r\n                        },\r\n                        jsPDF: {\r\n                            unit: 'mm',\r\n                            format: [98, 225], // Envelope #9 size: 3.875\" x 8.875\" = 98mm x 225mm\r\n                            orientation: 'portrait',\r\n                            compress: true\r\n                        }\r\n                    } : {\r\n                        margin: [5, 5, 5, 5], // Standard margins for big receipt\r\n                        filename: fileName,\r\n                        image: { type: 'jpeg', quality: 0.95 },\r\n                        html2canvas: {\r\n                            scale: 1.5,\r\n                            useCORS: true,\r\n                            logging: true,\r\n                            letterRendering: true,\r\n                            allowTaint: true,\r\n                            foreignObjectRendering: false,\r\n                            scrollY: 0,\r\n                            windowHeight: window.innerHeight * 2\r\n                        },\r\n                        jsPDF: {\r\n                            unit: 'mm',\r\n                            format: 'a4',\r\n                            orientation: 'portrait',\r\n                            compress: true\r\n                        }\r\n                    };\r\n\r\n                    // Wait for fonts and images to load\r\n                    await document.fonts.ready;\r\n\r\n                    // Wait for all images to load\r\n                    const images = Array.from(element.getElementsByTagName('img'));\r\n                    await Promise.all(images.map(img => {\r\n                        if (img.complete) return Promise.resolve();\r\n                        return new Promise(resolve => {\r\n                            img.onload = resolve;\r\n                            img.onerror = resolve;\r\n                        });\r\n                    }));\r\n\r\n                    // Force a small delay to ensure all styles are applied\r\n                    await new Promise(resolve => setTimeout(resolve, 500));\r\n\r\n                    // Generate PDF directly from the visible element\r\n                    const pdfResult = await window.html2pdf()\r\n                        .set(opt)\r\n                        .from(element)\r\n                        .output('blob') as Blob;\r\n\r\n                    console.log(\"PDF generated successfully\");\r\n\r\n                    // Create a direct download link\r\n                    const link = document.createElement('a');\r\n                    const url = window.URL.createObjectURL(new Blob([pdfResult], { type: 'application/pdf' }));\r\n                    link.href = url;\r\n                    link.download = fileName;\r\n\r\n                    // Wait a moment before clicking to ensure everything is ready\r\n                    await new Promise(resolve => setTimeout(resolve, 100));\r\n                    link.click();\r\n\r\n                    // Cleanup after a short delay\r\n                    setTimeout(() => {\r\n                        window.URL.revokeObjectURL(url);\r\n                    }, 100);\r\n\r\n                    // Upload to Appwrite in the background\r\n                    try {\r\n                        console.log(\"=== UPLOADING PDF TO APPWRITE ===\");\r\n\r\n                        // Create File object\r\n                        const file = new File([pdfResult], fileName, {\r\n                            type: 'application/pdf'\r\n                        });\r\n\r\n                        // Generate a unique file ID\r\n                        const fileId = ID.unique();\r\n                        console.log(\"File ID:\", fileId);\r\n\r\n                        // Upload to Appwrite with public read permission\r\n                        const uploadedFile = await storage.createFile(\r\n                            appwriteConfig.storageId,\r\n                            fileId,\r\n                            file,\r\n                            ['read(\"any\")'] // This makes the file publicly readable\r\n                        );\r\n                        console.log(\"File uploaded successfully:\", uploadedFile);\r\n\r\n                        // Get the file download URL\r\n                        const fileUrl = storage.getFileDownload(\r\n                            appwriteConfig.storageId,\r\n                            uploadedFile.$id\r\n                        ).toString();\r\n                        console.log(\"File URL:\", fileUrl);\r\n\r\n                        // Store the PDF reference in the PDF collection using the exact attribute names\r\n                        const pdfDocument = await databases.createDocument(\r\n                            appwriteConfig.databaseId,\r\n                            PDF_COLLECTION_ID,\r\n                            ID.unique(),\r\n                            {\r\n                                name: formData.customerName,\r\n                                receiptId: formData.receiptNumber,\r\n                                receiptPdf: fileUrl\r\n                            }\r\n                        );\r\n                        console.log(\"PDF document created in database:\", pdfDocument);\r\n\r\n                        // Increment the receipt ID after successful upload\r\n                        await incrementReceiptIdAfterUpload();\r\n                    } catch (uploadError) {\r\n                        console.error(\"Error uploading PDF to Appwrite:\", uploadError);\r\n                        console.log(\"Error details:\", JSON.stringify(uploadError, null, 2));\r\n                        // Don't show an error toast since this is a background operation\r\n                    }\r\n\r\n                    // Dismiss loading toast and show success\r\n                    toast.dismiss();\r\n                    toast.success('PDF generated successfully!');\r\n\r\n                    return pdfResult;\r\n                } catch (error) {\r\n                    console.error('PDF Generation Error:', error);\r\n                    console.log(\"Error details:\", JSON.stringify(error, null, 2));\r\n                    toast.dismiss();\r\n                    toast.error('Failed to generate PDF');\r\n                    throw error;\r\n                }\r\n            }\r\n            throw new Error('PDF generation not available');\r\n        } catch (error) {\r\n            console.error('PDF Generation Error:', error);\r\n            console.log(\"Error details:\", JSON.stringify(error, null, 2));\r\n            toast.dismiss();\r\n            toast.error('Failed to generate PDF');\r\n            throw error;\r\n        }\r\n    };\r\n\r\n    // Function to check if a receipt PDF already exists in Appwrite\r\n    const findExistingReceiptPDF = async (receiptNumber: string) => {\r\n        try {\r\n            console.log(\"=== CHECKING FOR EXISTING RECEIPT PDF ===\");\r\n            console.log(\"Looking for receipt number:\", receiptNumber);\r\n\r\n            // Query the PDF collection for the receipt ID\r\n            const response = await databases.listDocuments(\r\n                appwriteConfig.databaseId,\r\n                PDF_COLLECTION_ID,\r\n                [Query.equal(\"receiptId\", receiptNumber)]\r\n            );\r\n\r\n            console.log(\"Search results:\", response);\r\n\r\n            if (response.documents.length > 0) {\r\n                // Found existing receipt PDF\r\n                const existingDoc = response.documents[0];\r\n                console.log(\"Found existing receipt PDF:\", existingDoc);\r\n                return {\r\n                    exists: true,\r\n                    fileUrl: existingDoc.receiptPdf,\r\n                    document: existingDoc\r\n                };\r\n            }\r\n\r\n            // No existing receipt PDF found\r\n            console.log(\"No existing receipt PDF found\");\r\n            return { exists: false };\r\n        } catch (error) {\r\n            console.error(\"Error checking for existing receipt PDF:\", error);\r\n            console.log(\"Error details:\", JSON.stringify(error, null, 2));\r\n            return { exists: false, error };\r\n        }\r\n    };\r\n\r\n    // Function to increment receipt ID after successful upload\r\n    const incrementReceiptIdAfterUpload = async () => {\r\n        try {\r\n            console.log(\"=== INCREMENTING RECEIPT ID AFTER UPLOAD ===\");\r\n\r\n            // Get the current receipt ID\r\n            const currentIdNumber = formData.receiptIdNumber || 1000;\r\n            const newId = currentIdNumber + 1;\r\n            console.log(\"Current ID:\", currentIdNumber);\r\n            console.log(\"New ID:\", newId);\r\n\r\n            // Update the ID in Appwrite\r\n            const updated = await updateReceiptId(newId);\r\n\r\n            if (updated) {\r\n                console.log(\"Receipt ID incremented successfully\");\r\n\r\n                // Update the form data with the new receipt ID\r\n                setFormData(prev => ({\r\n                    ...prev,\r\n                    receiptNumber: `CEP${newId}`,\r\n                    receiptIdNumber: newId\r\n                }));\r\n\r\n                console.log(\"Form data updated with new receipt ID\");\r\n                return true;\r\n            } else {\r\n                console.error(\"Failed to increment receipt ID\");\r\n                return false;\r\n            }\r\n        } catch (error) {\r\n            console.error(\"Error incrementing receipt ID:\", error);\r\n            console.log(\"Error details:\", JSON.stringify(error, null, 2));\r\n            return false;\r\n        }\r\n    };\r\n\r\n    // Function to generate PDF blob without opening it in a new tab\r\n    const generatePDFBlobOnly = async () => {\r\n        try {\r\n            console.log(\"=== GENERATING PDF BLOB ONLY ===\");\r\n\r\n            if (typeof window.html2pdf === 'function' && receiptRef.current) {\r\n                const element = receiptRef.current;\r\n                const fileName = `${formData.receiptNumber}_${formData.customerName.replace(/\\s+/g, '_')}.pdf`;\r\n\r\n                console.log(\"Generating PDF blob for:\", fileName);\r\n\r\n                // Set options for the PDF based on receipt size\r\n                const opt: Html2PdfOptions = receiptSize === 'mini' ? {\r\n                    margin: [3, 3, 3, 3], // Envelope #9 compatible margins\r\n                    filename: fileName,\r\n                    image: { type: 'jpeg', quality: 0.95 },\r\n                    html2canvas: {\r\n                        scale: 2.5, // Higher scale for better text clarity on envelope size\r\n                        useCORS: true,\r\n                        logging: true,\r\n                        letterRendering: true,\r\n                        allowTaint: true,\r\n                        foreignObjectRendering: false,\r\n                        scrollY: 0,\r\n                        windowHeight: window.innerHeight * 2\r\n                    },\r\n                    jsPDF: {\r\n                        unit: 'mm',\r\n                        format: [98, 225], // Envelope #9 size: 3.875\" x 8.875\" = 98mm x 225mm\r\n                        orientation: 'portrait',\r\n                        compress: true\r\n                    }\r\n                } : {\r\n                    margin: [5, 5, 5, 5],\r\n                    filename: fileName,\r\n                    image: { type: 'jpeg', quality: 0.95 },\r\n                    html2canvas: {\r\n                        scale: 1.5,\r\n                        useCORS: true,\r\n                        logging: true,\r\n                        letterRendering: true,\r\n                        allowTaint: true,\r\n                        foreignObjectRendering: false,\r\n                        scrollY: 0,\r\n                        windowHeight: window.innerHeight * 2\r\n                    },\r\n                    jsPDF: {\r\n                        unit: 'mm',\r\n                        format: 'a4',\r\n                        orientation: 'portrait',\r\n                        compress: true\r\n                    }\r\n                };\r\n\r\n                // Wait for fonts and images to load\r\n                await document.fonts.ready;\r\n\r\n                // Wait for all images to load\r\n                const images = Array.from(element.getElementsByTagName('img'));\r\n                await Promise.all(images.map(img => {\r\n                    if (img.complete) return Promise.resolve();\r\n                    return new Promise(resolve => {\r\n                        img.onload = resolve;\r\n                        img.onerror = resolve;\r\n                    });\r\n                }));\r\n\r\n                // Force a small delay to ensure all styles are applied\r\n                await new Promise(resolve => setTimeout(resolve, 500));\r\n\r\n                // Generate PDF blob\r\n                const pdfBlob = await window.html2pdf()\r\n                    .set(opt)\r\n                    .from(element)\r\n                    .output('blob') as Blob;\r\n\r\n                console.log(\"PDF blob generated successfully\");\r\n                return pdfBlob;\r\n            }\r\n\r\n            throw new Error('PDF generation not available');\r\n        } catch (error) {\r\n            console.error('Error generating PDF blob:', error);\r\n            console.log(\"Error details:\", JSON.stringify(error, null, 2));\r\n            throw error;\r\n        }\r\n    };\r\n\r\n    // Function to upload PDF to Appwrite and return the file URL\r\n    const uploadPDFToAppwrite = async (pdfBlob: Blob) => {\r\n        try {\r\n            console.log(\"=== UPLOADING PDF TO APPWRITE ===\");\r\n\r\n            // Create File object\r\n            const fileName = `${formData.receiptNumber}_${formData.customerName.replace(/\\s+/g, '_')}.pdf`;\r\n            const file = new File([pdfBlob], fileName, {\r\n                type: 'application/pdf'\r\n            });\r\n\r\n            console.log(\"File name:\", fileName);\r\n\r\n            // Generate a unique file ID\r\n            const fileId = ID.unique();\r\n            console.log(\"File ID:\", fileId);\r\n\r\n            // Upload to Appwrite with public read permission\r\n            const uploadedFile = await storage.createFile(\r\n                appwriteConfig.storageId,\r\n                fileId,\r\n                file,\r\n                ['read(\"any\")'] // This makes the file publicly readable\r\n            );\r\n            console.log(\"File uploaded successfully:\", uploadedFile);\r\n\r\n            // Get the file download URL\r\n            const fileUrl = storage.getFileDownload(\r\n                appwriteConfig.storageId,\r\n                uploadedFile.$id\r\n            ).toString();\r\n            console.log(\"File URL:\", fileUrl);\r\n\r\n            // Store the PDF reference in the PDF collection using the exact attribute names\r\n            const pdfDocument = await databases.createDocument(\r\n                appwriteConfig.databaseId,\r\n                PDF_COLLECTION_ID,\r\n                ID.unique(),\r\n                {\r\n                    name: formData.customerName,\r\n                    receiptId: formData.receiptNumber,\r\n                    receiptPdf: fileUrl\r\n                }\r\n            );\r\n            console.log(\"PDF document created in database:\", pdfDocument);\r\n\r\n            // Increment the receipt ID after successful upload\r\n            await incrementReceiptIdAfterUpload();\r\n\r\n            return fileUrl;\r\n        } catch (error) {\r\n            console.error(\"Error uploading PDF to Appwrite:\", error);\r\n            console.log(\"Error details:\", JSON.stringify(error, null, 2));\r\n            throw error;\r\n        }\r\n    };\r\n\r\n    const sendWhatsApp = async () => {\r\n        try {\r\n            toast.loading(\r\n                <div className=\"flex items-center gap-2\">\r\n                    <SpinningLoader size=\"small\" />\r\n                    <span>Preparing receipt for WhatsApp...</span>\r\n                </div>\r\n            );\r\n\r\n            let fileUrl = \"\";\r\n\r\n            // First, check if the receipt PDF already exists in Appwrite\r\n            const existingPDF = await findExistingReceiptPDF(formData.receiptNumber);\r\n\r\n            if (existingPDF.exists && existingPDF.fileUrl) {\r\n                // Use the existing PDF\r\n                console.log(\"Using existing PDF file URL:\", existingPDF.fileUrl);\r\n                fileUrl = existingPDF.fileUrl;\r\n\r\n                // No need to increment receipt ID since we're using an existing PDF\r\n                console.log(\"Using existing PDF, not incrementing receipt ID\");\r\n            } else {\r\n                // No existing PDF found, generate and upload a new one\r\n                console.log(\"No existing PDF found, generating a new one\");\r\n\r\n                try {\r\n                    // Generate the PDF blob without opening it\r\n                    const pdfBlob = await generatePDFBlobOnly();\r\n\r\n                    // Upload the PDF to Appwrite\r\n                    fileUrl = await uploadPDFToAppwrite(pdfBlob);\r\n                } catch (error) {\r\n                    console.error(\"Error generating or uploading PDF:\", error);\r\n                    toast.dismiss();\r\n                    toast.error('Failed to prepare receipt for WhatsApp');\r\n                    return;\r\n                }\r\n            }\r\n\r\n            // Share via WhatsApp\r\n            const message = encodeURIComponent(\r\n                `Hello ${formData.customerName}, here's your receipt ${formData.receiptNumber} for ₦${formData.subtotal.toLocaleString()}. Thank you for shopping with us!\\n\\nDownload Receipt: ${fileUrl}`\r\n            );\r\n\r\n            // Format the WhatsApp number\r\n            let phone = formData.whatsapp.replace(/[^0-9]/g, '');\r\n\r\n            // Add country code if not present\r\n            if (!phone.startsWith('234') && !phone.startsWith('+234')) {\r\n                // If the number starts with 0, replace it with 234\r\n                if (phone.startsWith('0')) {\r\n                    phone = '234' + phone.substring(1);\r\n                } else {\r\n                    // Otherwise, just add 234 prefix\r\n                    phone = '234' + phone;\r\n                }\r\n            }\r\n\r\n            // Remove + if present\r\n            phone = phone.replace('+', '');\r\n\r\n            console.log(\"Formatted WhatsApp number:\", phone);\r\n\r\n            // Create WhatsApp URL\r\n            const whatsappUrl = `https://wa.me/${phone}?text=${message}`;\r\n            console.log(\"WhatsApp URL:\", whatsappUrl);\r\n\r\n            // Dismiss loading toast\r\n            toast.dismiss();\r\n\r\n            // Open WhatsApp in a new tab\r\n            window.open(whatsappUrl, '_blank');\r\n\r\n            toast.success('Receipt sent to WhatsApp!');\r\n        } catch (error) {\r\n            console.error('Error sharing receipt:', error);\r\n            console.log(\"Error details:\", JSON.stringify(error, null, 2));\r\n            toast.dismiss();\r\n            toast.error('Failed to share receipt');\r\n        }\r\n    };\r\n\r\n\r\n\r\n    const resetForm = async () => {\r\n        // Generate a new receipt ID\r\n        try {\r\n            setIsLoadingId(true);\r\n            console.log(\"=== RESET FORM - RECEIPT ID FETCH ===\");\r\n\r\n            let currentId = 1000;\r\n            let newId = 1001;\r\n\r\n            // Try to get the current ID\r\n            console.log(\"Attempting to get document with ID:\", RECEIPT_COUNTER_ID);\r\n\r\n            try {\r\n                const response = await databases.getDocument(\r\n                    appwriteConfig.databaseId,\r\n                    RECEIPT_COLLECTION_ID,\r\n                    RECEIPT_COUNTER_ID\r\n                );\r\n\r\n                console.log(\"getDocument response in resetForm:\", response);\r\n                console.log(\"Raw receiptId from getDocument:\", response.receiptId);\r\n                console.log(\"receiptId type:\", typeof response.receiptId);\r\n\r\n                // Clear indication if receipt ID is coming through from Appwrite in resetForm\r\n                console.log(\"✅ RECEIPT ID FROM APPWRITE (resetForm):\",\r\n                    response.receiptId ? \"YES - Value: \" + response.receiptId : \"NO - Value is missing or null\");\r\n\r\n                // Get the current ID if document exists and convert from string to number\r\n                const idFromDb = response.receiptId ? parseInt(response.receiptId, 10) : 1000;\r\n                console.log(\"Parsed receiptId to number:\", idFromDb);\r\n\r\n                // Use 1000 as fallback if parsing results in NaN\r\n                currentId = isNaN(idFromDb) ? 1000 : idFromDb;\r\n                console.log(\"Final currentId after validation:\", currentId);\r\n\r\n                // Increment the ID\r\n                newId = currentId + 1;\r\n                console.log(\"New ID after increment:\", newId);\r\n            } catch (getError) {\r\n                console.error('Error getting receipt counter, using default:', getError);\r\n                console.log(\"Error details:\", JSON.stringify(getError, null, 2));\r\n                console.log(\"Using default ID values\");\r\n                // If document doesn't exist, we'll use the default values\r\n            }\r\n\r\n            // Try to update the ID in Appwrite\r\n            try {\r\n                console.log(\"Updating receipt ID in Appwrite to:\", newId);\r\n                const updated = await updateReceiptId(newId);\r\n                console.log(\"Update result:\", updated);\r\n            } catch (updateError) {\r\n                console.error('Error updating receipt ID:', updateError);\r\n                console.log(\"Error details:\", JSON.stringify(updateError, null, 2));\r\n                console.log(\"Continuing with the new ID even if update fails\");\r\n                // Continue with the new ID even if update fails\r\n            }\r\n\r\n            // Reset the form with the new receipt ID\r\n            console.log(\"Resetting form with new receipt ID:\", newId);\r\n            setFormData({\r\n                customerName: '',\r\n                whatsapp: '',\r\n                items: [{ description: '', quantity: 1, unitPrice: 0, total: 0 }],\r\n                subtotal: 0,\r\n                amountPaid: 0,\r\n                balance: 0,\r\n                receiptNumber: `CEP${newId}`,\r\n                receiptIdNumber: newId,\r\n                date: new Date().toISOString().split('T')[0],\r\n            });\r\n            console.log(\"Form reset with new receipt number:\", `CEP${newId}`);\r\n        } catch (error) {\r\n            console.error('Error generating new receipt ID:', error);\r\n            console.log(\"Error details:\", JSON.stringify(error, null, 2));\r\n\r\n            // Fallback to timestamp-based ID\r\n            const timestamp = new Date().getTime().toString().slice(-6);\r\n            console.log(\"Using fallback timestamp-based ID:\", timestamp);\r\n\r\n            setFormData({\r\n                customerName: '',\r\n                whatsapp: '',\r\n                items: [{ description: '', quantity: 1, unitPrice: 0, total: 0 }],\r\n                subtotal: 0,\r\n                amountPaid: 0,\r\n                balance: 0,\r\n                receiptNumber: `CEP${timestamp}`,\r\n                date: new Date().toISOString().split('T')[0],\r\n            });\r\n            console.log(\"Form reset with fallback receipt number:\", `CEP${timestamp}`);\r\n        } finally {\r\n            setIsLoadingId(false);\r\n            setShowPreview(false);\r\n            console.log(\"=== RESET FORM COMPLETE ===\");\r\n        }\r\n    };\r\n\r\n    const startNewReceipt = () => {\r\n        if (window.confirm('Are you sure you want to start a new receipt? This will clear the current one.')) {\r\n            resetForm();\r\n        }\r\n    };\r\n\r\n    return (\r\n        <div className=\"min-h-screen bg-gray-50 mt-32 pb-4 sm:pb-8 pt-6 sm:pt-8 md:pt-10\">\r\n            <Script src=\"https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js\" />\r\n\r\n            <div className=\"max-w-7xl mx-auto px-3 sm:px-6 lg:px-8\">\r\n                {/* Back button with animation */}\r\n                <motion.div\r\n                    whileHover={{ scale: 1.05 }}\r\n                    whileTap={{ scale: 0.95 }}\r\n                    className=\"inline-block mb-6\"\r\n                >\r\n                    <Link\r\n                        href=\"/admin\"\r\n                        className=\"inline-flex items-center px-3 py-2 rounded-lg text-gray-700 hover:text-black hover:bg-gray-100 transition-all duration-200\"\r\n                    >\r\n                        <svg\r\n                            xmlns=\"http://www.w3.org/2000/svg\"\r\n                            className=\"h-5 w-5 mr-2\"\r\n                            fill=\"none\"\r\n                            viewBox=\"0 0 24 24\"\r\n                            stroke=\"currentColor\"\r\n                        >\r\n                            <path\r\n                                strokeLinecap=\"round\"\r\n                                strokeLinejoin=\"round\"\r\n                                strokeWidth={2}\r\n                                d=\"M10 19l-7-7m0 0l7-7m-7 7h18\"\r\n                            />\r\n                        </svg>\r\n                        Back to Admin\r\n                    </Link>\r\n                </motion.div>\r\n\r\n                <div className=\"mb-6 sm:mb-8\">\r\n                    <h1 className=\"text-xl sm:text-2xl md:text-3xl font-bold text-gray-900\">Receipt Generator</h1>\r\n                </div>\r\n\r\n                <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-3 sm:gap-6 lg:items-start\">\r\n                    <div className=\"bg-white p-4 sm:p-6 rounded-xl shadow-lg\">\r\n                        <form onSubmit={handleSubmit} className=\"space-y-4\">\r\n                            <div className=\"space-y-3 sm:space-y-4\">\r\n                                <div>\r\n                                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                                        Customer Name\r\n                                    </label>\r\n                                    <input\r\n                                        type=\"text\"\r\n                                        name=\"customerName\"\r\n                                        value={formData.customerName}\r\n                                        onChange={handleChange}\r\n                                        required\r\n                                        className=\"w-full px-3 sm:px-4 py-2 text-sm sm:text-base border border-gray-300 rounded-lg focus:ring-1 focus:ring-black focus:border-black text-gray-900 placeholder-gray-500\"\r\n                                    />\r\n                                </div>\r\n\r\n                                <div>\r\n                                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                                        Date\r\n                                    </label>\r\n                                    <input\r\n                                        type=\"date\"\r\n                                        name=\"date\"\r\n                                        value={formData.date}\r\n                                        onChange={handleChange}\r\n                                        required\r\n                                        className=\"w-full px-3 sm:px-4 py-2 text-sm sm:text-base border border-gray-300 rounded-lg focus:ring-1 focus:ring-black focus:border-black text-gray-900\"\r\n                                    />\r\n                                </div>\r\n\r\n                                <div>\r\n                                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                                        WhatsApp Number\r\n                                    </label>\r\n                                    <input\r\n                                        type=\"tel\"\r\n                                        name=\"whatsapp\"\r\n                                        value={formData.whatsapp}\r\n                                        onChange={handleChange}\r\n                                        required\r\n                                        placeholder=\"+234...\"\r\n                                        className=\"w-full px-3 sm:px-4 py-2 text-sm sm:text-base border border-gray-300 rounded-lg focus:ring-1 focus:ring-black focus:border-black text-gray-900 placeholder-gray-500\"\r\n                                    />\r\n                                </div>\r\n\r\n                                <div>\r\n                                    <div className=\"flex justify-between items-center mb-1\">\r\n                                        <label className=\"block text-sm font-medium text-gray-700\">\r\n                                            Receipt Number\r\n                                        </label>\r\n                                        <button\r\n                                            type=\"button\"\r\n                                            onClick={() => setShowIdSettings(!showIdSettings)}\r\n                                            className=\"text-xs text-blue-600 hover:text-blue-800\"\r\n                                        >\r\n                                            {showIdSettings ? 'Hide Settings' : 'ID Settings'}\r\n                                        </button>\r\n                                    </div>\r\n\r\n                                    <div className=\"relative\">\r\n                                        <input\r\n                                            type=\"text\"\r\n                                            name=\"receiptNumber\"\r\n                                            value={formData.receiptNumber}\r\n                                            readOnly\r\n                                            className=\"w-full px-3 sm:px-4 py-2 text-sm sm:text-base border border-gray-300 rounded-lg focus:ring-1 focus:ring-black focus:border-black text-gray-900 placeholder-gray-500 bg-gray-50\"\r\n                                        />\r\n                                        <button\r\n                                            type=\"button\"\r\n                                            onClick={generateNewReceiptId}\r\n                                            disabled={isLoadingId}\r\n                                            className=\"absolute right-2 top-1/2 transform -translate-y-1/2 text-xs bg-gray-100 hover:bg-gray-200 text-gray-700 px-2 py-1 rounded\"\r\n                                        >\r\n                                            {isLoadingId ? 'Loading...' : 'Generate New ID'}\r\n                                        </button>\r\n                                    </div>\r\n\r\n                                    {showIdSettings && (\r\n                                        <div className=\"mt-2 p-3 border border-gray-200 rounded-lg bg-gray-50\">\r\n                                            <p className=\"text-xs text-gray-600 mb-2\">Set a custom starting ID number:</p>\r\n                                            <div className=\"flex gap-2\">\r\n                                                <input\r\n                                                    type=\"number\"\r\n                                                    value={startingIdNumber}\r\n                                                    onChange={(e) => setStartingIdNumber(e.target.value === '' ? '' : Number(e.target.value))}\r\n                                                    placeholder=\"e.g. 1000\"\r\n                                                    min=\"1000\"\r\n                                                    className=\"flex-1 px-3 py-1 text-sm border border-gray-300 rounded-lg\"\r\n                                                />\r\n                                                <button\r\n                                                    type=\"button\"\r\n                                                    onClick={setCustomStartingId}\r\n                                                    disabled={isLoadingId}\r\n                                                    className=\"text-xs bg-blue-600 hover:bg-blue-700 text-white px-2 py-1 rounded flex items-center justify-center\"\r\n                                                >\r\n                                                    {isLoadingId ? (\r\n                                                        <div className=\"flex items-center gap-1\">\r\n                                                            <SpinningLoader size=\"small\" />\r\n                                                            <span>Setting...</span>\r\n                                                        </div>\r\n                                                    ) : 'Set ID'}\r\n                                                </button>\r\n                                            </div>\r\n                                            <p className=\"text-xs text-gray-500 mt-1\">Current ID: {formData.receiptIdNumber || 'Not set'}</p>\r\n                                        </div>\r\n                                    )}\r\n                                </div>\r\n                            </div>\r\n\r\n                            <div className=\"space-y-3 sm:space-y-4\">\r\n                                <div className=\"flex justify-between items-center\">\r\n                                    <h3 className=\"text-base sm:text-lg text-gray-700 font-semibold\">Items</h3>\r\n                                    <button\r\n                                        type=\"button\"\r\n                                        onClick={addLineItem}\r\n                                        className=\"text-xs sm:text-sm bg-gray-700 text-white px-2 sm:px-3 py-1 rounded-lg hover:bg-gray-600\"\r\n                                    >\r\n                                        + Add Item\r\n                                    </button>\r\n                                </div>\r\n\r\n                                <div className=\"space-y-2 -mx-2 px-2 max-w-full overflow-x-auto\">\r\n                                    {formData.items.map((item, index) => (\r\n                                        <div key={index} className=\"flex items-center gap-1.5 sm:gap-2 min-w-max sm:min-w-0\">\r\n                                            <input\r\n                                                type=\"text\"\r\n                                                placeholder=\"Description\"\r\n                                                value={item.description}\r\n                                                onChange={(e) => handleItemChange(index, 'description', e.target.value)}\r\n                                                className=\"flex-1 min-w-[100px] px-2 py-2 text-xs sm:text-sm border border-gray-300 rounded-lg focus:ring-1 focus:ring-black focus:border-black text-gray-900 placeholder-gray-500\"\r\n                                            />\r\n\r\n                                            <input\r\n                                                type=\"number\"\r\n                                                placeholder=\"Qty\"\r\n                                                value={item.quantity === 0 ? '' : item.quantity}\r\n                                                onChange={(e) => {\r\n                                                    const value = e.target.value === '' ? 0 : Number(e.target.value);\r\n                                                    handleItemChange(index, 'quantity', value);\r\n                                                }}\r\n                                                onFocus={(e) => {\r\n                                                    if (e.target.value === '1' || e.target.value === '0') {\r\n                                                        e.target.select();\r\n                                                    }\r\n                                                }}\r\n                                                className=\"w-14 sm:w-20 px-1 sm:px-3 py-2 text-xs sm:text-sm border border-gray-300 rounded-lg focus:ring-1 focus:ring-black focus:border-black text-gray-900 placeholder-gray-500\"\r\n                                                min=\"1\"\r\n                                                step=\"1\"\r\n                                                inputMode=\"numeric\"\r\n                                            />\r\n                                            <input\r\n                                                type=\"number\"\r\n                                                placeholder=\"Price ₦\"\r\n                                                value={item.unitPrice === 0 ? '' : item.unitPrice}\r\n                                                onChange={(e) => {\r\n                                                    const value = e.target.value === '' ? 0 : Number(e.target.value);\r\n                                                    handleItemChange(index, 'unitPrice', value);\r\n                                                }}\r\n                                                onFocus={(e) => e.target.select()}\r\n                                                className=\"w-16 sm:w-24 px-1 sm:px-3 py-2 text-xs sm:text-sm border border-gray-300 rounded-lg focus:ring-1 focus:ring-black focus:border-black text-gray-900 placeholder-gray-500\"\r\n                                                min=\"0\"\r\n                                                step=\"1\"\r\n                                                inputMode=\"numeric\"\r\n                                            />\r\n                                            <div className=\"flex items-center gap-1 sm:gap-2 min-w-[70px] sm:min-w-[100px] justify-end\">\r\n                                                <span className=\"text-xs sm:text-sm text-gray-900 whitespace-nowrap font-medium transition-all duration-300\" style={{ color: item.total > 0 ? '#000' : '#888' }}>\r\n                                                    ₦{item.total.toLocaleString()}\r\n                                                </span>\r\n                                                <button\r\n                                                    type=\"button\"\r\n                                                    onClick={() => removeLineItem(index)}\r\n                                                    className=\"p-1 text-red-500 hover:text-red-700 rounded-full hover:bg-red-50\"\r\n                                                    aria-label=\"Remove item\"\r\n                                                    title=\"Remove item\"\r\n                                                >\r\n                                                    ×\r\n                                                </button>\r\n                                            </div>\r\n                                        </div>\r\n                                    ))}\r\n                                </div>\r\n                            </div>\r\n\r\n                            <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4\">\r\n                                <div>\r\n                                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                                        Amount Paid (₦)\r\n                                    </label>\r\n                                    <input\r\n                                        type=\"number\"\r\n                                        placeholder=\"Enter amount paid\"\r\n                                        value={formData.amountPaid === 0 ? '' : formData.amountPaid}\r\n                                        onChange={(e) => {\r\n                                            const value = e.target.value === '' ? 0 : Number(e.target.value);\r\n                                            setFormData(prev => ({\r\n                                                ...prev,\r\n                                                amountPaid: value,\r\n                                                balance: prev.subtotal - value // Calculate balance in real-time\r\n                                            }));\r\n                                        }}\r\n                                        onFocus={(e) => e.target.select()}\r\n                                        className=\"w-full px-3 sm:px-4 py-2 text-sm sm:text-base border rounded-lg text-gray-900 placeholder-gray-500\"\r\n                                        min=\"0\"\r\n                                        step=\"1\"\r\n                                    />\r\n                                </div>\r\n                                <div className=\"text-right space-y-2 text-gray-900 font-medium text-sm sm:text-base\">\r\n                                    <p className=\"transition-all duration-300\">\r\n                                        <span className=\"inline-block w-20 text-gray-600\">Subtotal:</span>\r\n                                        <span className=\"font-semibold\">₦{formData.subtotal.toLocaleString()}</span>\r\n                                    </p>\r\n                                    <p className=\"transition-all duration-300\">\r\n                                        <span className=\"inline-block w-20 text-gray-600\">Balance:</span>\r\n                                        <span className={`font-semibold ${formData.balance > 0 ? 'text-red-600' : 'text-green-600'}`}>\r\n                                            ₦{formData.balance.toLocaleString()}\r\n                                        </span>\r\n                                    </p>\r\n                                </div>\r\n                            </div>\r\n                            <div className=\"flex justify-between gap-4 mt-6\">\r\n                                <button\r\n                                    type=\"submit\"\r\n                                    className=\"w-full sm:flex-1 bg-[#333333] text-white px-4 py-2 rounded-lg hover:bg-gray-800 text-sm sm:text-base\"\r\n                                >\r\n                                    Generate Receipt\r\n                                </button>\r\n                                <button\r\n                                    type=\"button\"\r\n                                    onClick={resetForm}\r\n                                    className=\"w-full sm:flex-1 px-4 py-2 border-gray-400 text-gray-600 border-2 rounded-lg hover:bg-gray-600 hover:text-white text-sm sm:text-base\"\r\n                                >\r\n                                    Reset\r\n                                </button>\r\n                            </div>\r\n                        </form>\r\n                    </div>\r\n\r\n                    {showPreview && (\r\n                        <div className=\"bg-white p-2 sm:p-4 md:p-6 rounded-xl shadow-lg overflow-auto\"\r\n                            style={{\r\n                                maxHeight: 'calc(100vh - 180px)',\r\n                                height: 'auto',\r\n                                overscrollBehavior: 'contain'\r\n                            }}>\r\n                            <div\r\n                                ref={receiptRef}\r\n                                className={`bg-white mx-auto ${receiptSize === 'mini' ? 'scale-100' : 'sm:scale-90 md:scale-85'}`}\r\n                                style={receiptSize === 'mini' ? {\r\n                                    // Mini Receipt Styles (Envelope #9 compatible) - Optimized for thermal printing\r\n                                    width: '280px', // Envelope #9 width ≈ 280px (98mm)\r\n                                    maxWidth: '280px',\r\n                                    height: 'auto',\r\n                                    padding: '16px',\r\n                                    boxSizing: 'border-box',\r\n                                    backgroundColor: 'white',\r\n                                    fontFamily: 'Arial, sans-serif',\r\n                                    fontSize: '16px', // Increased from 12px for better thermal printing\r\n                                    fontWeight: '700', // Extra bold for thermal printing clarity\r\n                                    lineHeight: '1.5', // Better spacing for readability\r\n                                    border: '1px solid #ddd',\r\n                                    position: 'relative',\r\n                                    margin: '0 auto',\r\n                                    color: '#000000',\r\n                                    pageBreakInside: 'avoid'\r\n                                } : {\r\n                                    // Big Receipt Styles (A4)\r\n                                    width: '100%',\r\n                                    maxWidth: '210mm',\r\n                                    height: 'auto',\r\n                                    padding: '5mm',\r\n                                    boxSizing: 'border-box',\r\n                                    backgroundColor: 'white',\r\n                                    fontFamily: 'Arial, sans-serif',\r\n                                    fontSize: '10pt',\r\n                                    lineHeight: '1.4',\r\n                                    letterSpacing: '0.2px',\r\n                                    border: '1px solid #eee',\r\n                                    position: 'relative',\r\n                                    margin: '0 auto',\r\n                                    transformOrigin: 'top center',\r\n                                    color: '#000000',\r\n                                    pageBreakInside: 'avoid'\r\n                                }}\r\n                            >\r\n                                {receiptSize === 'mini' ? (\r\n                                    // MINI RECEIPT LAYOUT (58mm thermal printer)\r\n                                    <>\r\n                                        {/* Mini Header */}\r\n                                        <div className=\"text-center mb-4\">\r\n                                            <Image\r\n                                                src=\"/logo.png\"\r\n                                                alt=\"Cepoka Logo\"\r\n                                                width={50}\r\n                                                height={50}\r\n                                                className=\"mx-auto mb-2\"\r\n                                                style={{\r\n                                                    maxWidth: '50px',\r\n                                                    height: 'auto',\r\n                                                    display: 'block',\r\n                                                    margin: '0 auto 10px auto'\r\n                                                }}\r\n                                                priority={true}\r\n                                                unoptimized={true}\r\n                                            />\r\n                                            <h2 style={{\r\n                                                fontSize: '18px', // Increased from 14px\r\n                                                fontWeight: '800', // Extra bold\r\n                                                color: '#000000',\r\n                                                marginBottom: '6px',\r\n                                                textAlign: 'center',\r\n                                                letterSpacing: '0.5px'\r\n                                            }}>\r\n                                                CEPOKA BEAUTY HUB\r\n                                            </h2>\r\n                                            <div style={{\r\n                                                fontSize: '12px', // Increased from 9px\r\n                                                fontWeight: '600',\r\n                                                color: '#000000',\r\n                                                textAlign: 'center',\r\n                                                marginBottom: '10px',\r\n                                                lineHeight: '1.4'\r\n                                            }}>\r\n                                                <div>Lekki, Lagos</div>\r\n                                                <div>+234 803 123 4567</div>\r\n                                            </div>\r\n                                        </div>\r\n\r\n                                        {/* Mini Customer Info */}\r\n                                        <div style={{\r\n                                            marginBottom: '14px',\r\n                                            fontSize: '13px', // Increased from 10px\r\n                                            fontWeight: '600',\r\n                                            color: '#000000',\r\n                                            borderTop: '2px dashed #000', // Thicker border\r\n                                            borderBottom: '2px dashed #000',\r\n                                            padding: '8px 0' // More padding\r\n                                        }}>\r\n                                            <div style={{ marginBottom: '4px', lineHeight: '1.4' }}>\r\n                                                <strong>Customer:</strong> {formData.customerName}\r\n                                            </div>\r\n                                            <div style={{ marginBottom: '4px', lineHeight: '1.4' }}>\r\n                                                <strong>Receipt #:</strong> {formData.receiptNumber}\r\n                                            </div>\r\n                                            <div style={{ marginBottom: '4px', lineHeight: '1.4' }}>\r\n                                                <strong>Tel:</strong> {formData.whatsapp}\r\n                                            </div>\r\n                                            <div style={{ lineHeight: '1.4' }}>\r\n                                                <strong>Date:</strong> {new Date(formData.date).toLocaleDateString('en-NG', {\r\n                                                    year: 'numeric',\r\n                                                    month: 'short',\r\n                                                    day: 'numeric'\r\n                                                })}\r\n                                            </div>\r\n                                        </div>\r\n\r\n                                        {/* Mini Items Table */}\r\n                                        <div style={{ marginBottom: '14px' }}>\r\n                                            <div style={{\r\n                                                fontSize: '14px', // Increased from 11px\r\n                                                fontWeight: '800', // Extra bold\r\n                                                textAlign: 'center',\r\n                                                marginBottom: '8px',\r\n                                                color: '#000000',\r\n                                                letterSpacing: '0.5px'\r\n                                            }}>\r\n                                                CASH SALES INVOICE\r\n                                            </div>\r\n\r\n                                            {formData.items.map((item, index) => (\r\n                                                <div key={index} style={{\r\n                                                    marginBottom: '6px', // More spacing\r\n                                                    fontSize: '12px', // Increased from 9px\r\n                                                    fontWeight: '600',\r\n                                                    color: '#000000',\r\n                                                    borderBottom: index < formData.items.length - 1 ? '1px dotted #666' : 'none',\r\n                                                    paddingBottom: '6px'\r\n                                                }}>\r\n                                                    <div style={{\r\n                                                        fontWeight: '700',\r\n                                                        marginBottom: '3px',\r\n                                                        fontSize: '13px' // Slightly larger for product names\r\n                                                    }}>\r\n                                                        {item.description}\r\n                                                    </div>\r\n                                                    <div style={{\r\n                                                        display: 'flex',\r\n                                                        justifyContent: 'space-between',\r\n                                                        fontSize: '12px',\r\n                                                        lineHeight: '1.3'\r\n                                                    }}>\r\n                                                        <span>{item.quantity} x ₦{item.unitPrice.toLocaleString()}</span>\r\n                                                        <span style={{ fontWeight: '700' }}>₦{item.total.toLocaleString()}</span>\r\n                                                    </div>\r\n                                                </div>\r\n                                            ))}\r\n                                        </div>\r\n\r\n                                        {/* Mini Totals */}\r\n                                        <div style={{\r\n                                            borderTop: '2px dashed #000', // Thicker border\r\n                                            paddingTop: '8px',\r\n                                            fontSize: '13px', // Increased from 10px\r\n                                            fontWeight: '600',\r\n                                            color: '#000000'\r\n                                        }}>\r\n                                            <div style={{\r\n                                                display: 'flex',\r\n                                                justifyContent: 'space-between',\r\n                                                marginBottom: '4px',\r\n                                                lineHeight: '1.4'\r\n                                            }}>\r\n                                                <strong>Subtotal:</strong>\r\n                                                <strong>₦{formData.subtotal.toLocaleString()}</strong>\r\n                                            </div>\r\n                                            <div style={{\r\n                                                display: 'flex',\r\n                                                justifyContent: 'space-between',\r\n                                                marginBottom: '4px',\r\n                                                lineHeight: '1.4'\r\n                                            }}>\r\n                                                <strong>Amount Paid:</strong>\r\n                                                <strong>₦{formData.amountPaid.toLocaleString()}</strong>\r\n                                            </div>\r\n                                            <div style={{\r\n                                                display: 'flex',\r\n                                                justifyContent: 'space-between',\r\n                                                borderTop: '2px solid #000', // Thicker border\r\n                                                paddingTop: '6px',\r\n                                                marginTop: '6px',\r\n                                                fontSize: '14px', // Larger for balance\r\n                                                fontWeight: '700'\r\n                                            }}>\r\n                                                <strong>Balance:</strong>\r\n                                                <strong style={{\r\n                                                    color: formData.balance > 0 ? '#cc0000' : '#008800'\r\n                                                }}>₦{formData.balance.toLocaleString()}</strong>\r\n                                            </div>\r\n                                        </div>\r\n\r\n                                        {/* Mini Footer */}\r\n                                        <div style={{\r\n                                            marginTop: '14px',\r\n                                            paddingTop: '8px',\r\n                                            borderTop: '2px dashed #000', // Thicker border\r\n                                            textAlign: 'center',\r\n                                            fontSize: '11px', // Increased from 8px\r\n                                            color: '#000000'\r\n                                        }}>\r\n                                            <div style={{\r\n                                                marginBottom: '4px',\r\n                                                fontWeight: '700',\r\n                                                fontSize: '12px' // Larger for thank you message\r\n                                            }}>\r\n                                                Thank you for your patronage!\r\n                                            </div>\r\n                                            <div style={{\r\n                                                marginBottom: '6px',\r\n                                                fontWeight: '600',\r\n                                                fontSize: '11px'\r\n                                            }}>\r\n                                                Follow us: @cepoka\r\n                                            </div>\r\n                                            <div style={{\r\n                                                fontSize: '9px', // Increased from 7px\r\n                                                color: '#666666',\r\n                                                fontWeight: '500'\r\n                                            }}>\r\n                                                Computer-generated receipt\r\n                                            </div>\r\n                                        </div>\r\n                                    </>\r\n                                ) : (\r\n                                    // BIG RECEIPT LAYOUT (A4) - Original layout\r\n                                    <>\r\n                                        <div className=\"text-center mb-8\">\r\n                                            <div className=\"mb-3\" style={{ height: '80px', position: 'relative' }}>\r\n                                                <Image\r\n                                                    src=\"/logo.png\"\r\n                                                    alt=\"Cepoka Logo\"\r\n                                                    width={80}\r\n                                                    height={80}\r\n                                                    className=\"mx-auto\"\r\n                                                    style={{\r\n                                                        maxWidth: '80px',\r\n                                                        height: 'auto',\r\n                                                        display: 'block',\r\n                                                        margin: '0 auto'\r\n                                                    }}\r\n                                                    priority={true}\r\n                                                    unoptimized={true}\r\n                                                />\r\n                                            </div>\r\n                                            <div className=\"relative pb-3 mb-6\">\r\n                                                <h2 style={{\r\n                                                    fontSize: '24pt',\r\n                                                    fontWeight: '700',\r\n                                                    color: '#000000',\r\n                                                    marginBottom: '8px'\r\n                                                }}>\r\n                                                    CEPOKA BEAUTY HUB\r\n                                                </h2>\r\n                                                <div className=\"absolute bottom-0 left-1/2 transform -translate-x-1/2 w-48 h-[2px]\"\r\n                                                    style={{\r\n                                                        background: '#000000',\r\n                                                        height: '2px'\r\n                                                    }}\r\n                                                />\r\n                                            </div>\r\n\r\n                                            {/* Address section with HEAD OFFICE and BRANCHES */}\r\n                                            <div className=\"flex flex-col items-center\" style={{\r\n                                                marginTop: '12px',\r\n                                                marginBottom: '16px',\r\n                                                fontSize: '9pt',\r\n                                                color: '#000000'\r\n                                            }}>\r\n                                                {/* HEAD OFFICE/SHOWROOM */}\r\n                                                <div className=\"mb-3 text-center\">\r\n                                                    <p style={{\r\n                                                        fontWeight: '700',\r\n                                                        marginBottom: '4px',\r\n                                                        fontSize: '10pt'\r\n                                                    }}>HEAD OFFICE/SHOWROOM</p>\r\n                                                    <div className=\"flex flex-wrap justify-center\">\r\n                                                        <span style={{ padding: '0 8px' }}>Lekki, Lagos</span>\r\n                                                        <span style={{ padding: '0 8px' }}>+234 803 123 4567</span>\r\n                                                        <span style={{ padding: '0 8px' }}><EMAIL></span>\r\n                                                    </div>\r\n                                                </div>\r\n\r\n                                                {/* BRANCHES */}\r\n                                                <div className=\"text-center\">\r\n                                                    <p style={{\r\n                                                        fontWeight: '700',\r\n                                                        marginBottom: '4px',\r\n                                                        fontSize: '10pt'\r\n                                                    }}>BRANCHES</p>\r\n                                                    <div className=\"flex flex-wrap justify-center\">\r\n                                                        <span style={{ padding: '0 8px' }}>Ikeja, Lagos</span>\r\n                                                        <span style={{ padding: '0 8px' }}>Abuja</span>\r\n                                                    </div>\r\n                                                </div>\r\n                                            </div>\r\n                                        </div>\r\n\r\n                                        <div className=\"mb-6\" style={{ padding: '12px', backgroundColor: '#f8f8f8', borderRadius: '8px' }}>\r\n                                            <div className=\"flex flex-col xs:flex-row justify-between mb-2\" style={{ color: '#000000' }}>\r\n                                                <p className=\"mb-1 xs:mb-0\" style={{ color: '#000000' }}>\r\n                                                    <strong style={{ fontWeight: '600' }}>Customer:</strong> {formData.customerName}\r\n                                                </p>\r\n                                                <p className=\"mb-1 xs:mb-0\" style={{ color: '#000000' }}>\r\n                                                    <strong style={{ fontWeight: '600' }}>Receipt #:</strong> {formData.receiptNumber}\r\n                                                </p>\r\n                                            </div>\r\n                                            <div className=\"flex flex-col xs:flex-row xs:justify-between\" style={{ color: '#000000' }}>\r\n                                                <p className=\"mb-1 xs:mb-0\" style={{ color: '#000000' }}>\r\n                                                    <strong style={{ fontWeight: '600' }}>Tel:</strong> {formData.whatsapp}\r\n                                                </p>\r\n                                                <p style={{ color: '#000000' }}>\r\n                                                    <strong style={{ fontWeight: '600' }}>Date:</strong> {new Date(formData.date).toLocaleDateString('en-NG', {\r\n                                                        year: 'numeric',\r\n                                                        month: 'long',\r\n                                                        day: 'numeric'\r\n                                                    })}\r\n                                                </p>\r\n                                            </div>\r\n                                        </div>\r\n\r\n                                        {/* CASH SALES INVOICE Header */}\r\n                                        <div className=\"text-center mb-3\">\r\n                                            <h3 style={{\r\n                                                fontWeight: '700',\r\n                                                fontSize: '14pt',\r\n                                                color: '#000000',\r\n                                                marginBottom: '8px'\r\n                                            }}>CASH SALES INVOICE</h3>\r\n                                        </div>\r\n\r\n                                        <div className=\"overflow-x-auto -mx-2 px-2\">\r\n                                            <table style={{\r\n                                                width: '100%',\r\n                                                borderCollapse: 'collapse',\r\n                                                marginBottom: '16px',\r\n                                                color: '#000000',\r\n                                                fontSize: '9pt',\r\n                                                minWidth: '300px'\r\n                                            }}>\r\n                                                <thead>\r\n                                                    <tr style={{ borderBottom: '2px solid #000000', borderTop: '2px solid #000000' }}>\r\n                                                        <th style={{\r\n                                                            padding: '8px 6px',\r\n                                                            textAlign: 'left',\r\n                                                            fontWeight: '700',\r\n                                                            color: '#000000'\r\n                                                        }}>Description</th>\r\n                                                        <th style={{\r\n                                                            padding: '8px 4px',\r\n                                                            textAlign: 'right',\r\n                                                            fontWeight: '700',\r\n                                                            color: '#000000'\r\n                                                        }}>Qty</th>\r\n                                                        <th style={{\r\n                                                            padding: '8px 4px',\r\n                                                            textAlign: 'right',\r\n                                                            fontWeight: '700',\r\n                                                            color: '#000000'\r\n                                                        }}>Price</th>\r\n                                                        <th style={{\r\n                                                            padding: '8px 4px',\r\n                                                            textAlign: 'right',\r\n                                                            fontWeight: '700',\r\n                                                            color: '#000000'\r\n                                                        }}>Total</th>\r\n                                                    </tr>\r\n                                                </thead>\r\n                                                <tbody>\r\n                                                    {formData.items.map((item, index) => (\r\n                                                        <tr key={index} style={{ borderBottom: '1px solid #e5e5e5' }}>\r\n                                                            <td style={{ padding: '8px 6px', color: '#000000' }}>{item.description}</td>\r\n                                                            <td style={{ padding: '8px 4px', textAlign: 'right', color: '#000000' }}>{item.quantity}</td>\r\n                                                            <td style={{ padding: '8px 4px', textAlign: 'right', color: '#000000' }}>₦{item.unitPrice.toLocaleString()}</td>\r\n                                                            <td style={{ padding: '8px 4px', textAlign: 'right', color: '#000000' }}>₦{item.total.toLocaleString()}</td>\r\n                                                        </tr>\r\n                                                    ))}\r\n                                                </tbody>\r\n                                            </table>\r\n                                        </div>\r\n\r\n                                        <div style={{\r\n                                            borderTop: '2px solid #000000',\r\n                                            paddingTop: '12px',\r\n                                            color: '#000000'\r\n                                        }}>\r\n                                            <div className=\"flex flex-col space-y-2\" style={{ color: '#000000' }}>\r\n                                                <div className=\"flex justify-between items-center\" style={{ color: '#000000' }}>\r\n                                                    <strong style={{\r\n                                                        fontWeight: '700',\r\n                                                        fontSize: '12pt',\r\n                                                        color: '#000000'\r\n                                                    }}>Subtotal:</strong>\r\n                                                    <span style={{\r\n                                                        fontWeight: '700',\r\n                                                        fontSize: '12pt',\r\n                                                        color: '#000000'\r\n                                                    }}>₦{formData.subtotal.toLocaleString()}</span>\r\n                                                </div>\r\n\r\n                                                <div className=\"flex justify-between items-center\" style={{ color: '#000000' }}>\r\n                                                    <strong style={{\r\n                                                        fontWeight: '700',\r\n                                                        fontSize: '12pt',\r\n                                                        color: '#000000'\r\n                                                    }}>Amount Paid:</strong>\r\n                                                    <span style={{\r\n                                                        fontWeight: '700',\r\n                                                        fontSize: '12pt',\r\n                                                        color: '#000000'\r\n                                                    }}>₦{formData.amountPaid.toLocaleString()}</span>\r\n                                                </div>\r\n\r\n                                                <div className=\"flex justify-between items-center mt-3 pt-2 border-t border-gray-200\" style={{ color: '#000000' }}>\r\n                                                    <strong style={{\r\n                                                        fontWeight: '700',\r\n                                                        fontSize: '12pt',\r\n                                                        color: '#000000'\r\n                                                    }}>Balance:</strong>\r\n                                                    <span style={{\r\n                                                        fontWeight: '700',\r\n                                                        fontSize: '12pt',\r\n                                                        color: formData.balance > 0 ? '#cc0000' : '#008800'\r\n                                                    }}>₦{formData.balance.toLocaleString()}</span>\r\n                                                </div>\r\n                                            </div>\r\n                                        </div>\r\n\r\n                                        <div style={{\r\n                                            marginTop: '16px',\r\n                                            paddingTop: '12px',\r\n                                            borderTop: '1px solid #e5e5e5',\r\n                                            textAlign: 'center',\r\n                                            color: '#000000',\r\n                                            fontSize: '9pt'\r\n                                        }}>\r\n                                            <p style={{\r\n                                                marginBottom: '3px',\r\n                                                fontWeight: '500',\r\n                                                color: '#000000'\r\n                                            }}>Thank you for your patronage!</p>\r\n                                            <p style={{\r\n                                                fontWeight: '500',\r\n                                                color: '#000000'\r\n                                            }}>Follow us on Instagram: @cepoka</p>\r\n                                            <div style={{\r\n                                                marginTop: '12px',\r\n                                                fontSize: '8pt',\r\n                                                color: '#666666'\r\n                                            }}>\r\n                                                This is a computer-generated receipt and requires no signature.\r\n                                            </div>\r\n                                        </div>\r\n                                    </>\r\n                                )}\r\n                            </div>\r\n\r\n                            <div className=\"sticky bottom-0 bg-white pt-3 pb-1 border-t mt-4\">\r\n                                <div className=\"flex flex-col sm:flex-row gap-2 sm:gap-4\">\r\n                                    <button\r\n                                        onClick={downloadPDF}\r\n                                        className=\"w-full sm:flex-1 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 text-sm sm:text-base\"\r\n                                    >\r\n                                        Download PDF\r\n                                    </button>\r\n                                    <button\r\n                                        onClick={sendWhatsApp}\r\n                                        className=\"w-full sm:flex-1 bg-[#25D366] text-white px-4 py-2 rounded-lg hover:bg-[#128C7E] text-sm sm:text-base\"\r\n                                    >\r\n                                        Send WhatsApp\r\n                                    </button>\r\n                                    <button\r\n                                        onClick={startNewReceipt}\r\n                                        className=\"w-full sm:flex-1 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 text-sm sm:text-base\"\r\n                                    >\r\n                                        New Receipt\r\n                                    </button>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    )}\r\n                </div>\r\n\r\n                {/* Receipt Size Selection Modal */}\r\n                {showSizeModal && (\r\n                    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\r\n                        <motion.div\r\n                            initial={{ opacity: 0, scale: 0.9 }}\r\n                            animate={{ opacity: 1, scale: 1 }}\r\n                            exit={{ opacity: 0, scale: 0.9 }}\r\n                            className=\"bg-white rounded-xl shadow-2xl max-w-md w-full p-6\"\r\n                        >\r\n                            <div className=\"text-center mb-6\">\r\n                                <h3 className=\"text-xl font-bold text-gray-900 mb-2\">\r\n                                    Choose Receipt Size\r\n                                </h3>\r\n                                <p className=\"text-gray-600 text-sm\">\r\n                                    What size of receipt do you want to generate?\r\n                                </p>\r\n                            </div>\r\n\r\n                            <div className=\"space-y-4\">\r\n                                {/* Big Receipt Option */}\r\n                                <button\r\n                                    onClick={() => handleSizeSelection('big')}\r\n                                    className=\"w-full p-4 border-2 border-gray-200 rounded-lg hover:border-blue-500 hover:bg-blue-50 transition-all duration-200 text-left group\"\r\n                                >\r\n                                    <div className=\"flex items-center justify-between\">\r\n                                        <div>\r\n                                            <h4 className=\"font-semibold text-gray-900 group-hover:text-blue-700\">\r\n                                                📄 Big Receipt (A4)\r\n                                            </h4>\r\n                                            <p className=\"text-sm text-gray-600 mt-1\">\r\n                                                Standard A4 size - Current default format\r\n                                            </p>\r\n                                        </div>\r\n                                        <div className=\"text-2xl group-hover:scale-110 transition-transform\">\r\n                                            📄\r\n                                        </div>\r\n                                    </div>\r\n                                </button>\r\n\r\n                                {/* Mini Receipt Option */}\r\n                                <button\r\n                                    onClick={() => handleSizeSelection('mini')}\r\n                                    className=\"w-full p-4 border-2 border-gray-200 rounded-lg hover:border-green-500 hover:bg-green-50 transition-all duration-200 text-left group\"\r\n                                >\r\n                                    <div className=\"flex items-center justify-between\">\r\n                                        <div>\r\n                                            <h4 className=\"font-semibold text-gray-900 group-hover:text-green-700\">\r\n                                                🧾 Mini Receipt (Envelope #9)\r\n                                            </h4>\r\n                                            <p className=\"text-sm text-gray-600 mt-1\">\r\n                                                For thermal printers - Envelope #9 size (3.875&quot; x 8.875&quot;)\r\n                                            </p>\r\n                                        </div>\r\n                                        <div className=\"text-2xl group-hover:scale-110 transition-transform\">\r\n                                            🧾\r\n                                        </div>\r\n                                    </div>\r\n                                </button>\r\n                            </div>\r\n\r\n                            <div className=\"mt-6 pt-4 border-t border-gray-200\">\r\n                                <button\r\n                                    onClick={() => setShowSizeModal(false)}\r\n                                    className=\"w-full px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors\"\r\n                                >\r\n                                    Cancel\r\n                                </button>\r\n                            </div>\r\n                        </motion.div>\r\n                    </div>\r\n                )}\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default ReceiptSender;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAuDA,0BAA0B;AAC1B;AACA;AACA;AAzDA;;;AAPA;;;;;;;AASA,qDAAqD;AACrD,MAAM,qBAAqB;AAC3B,0CAA0C;AAC1C,MAAM,wBAAwB;AAC9B,iCAAiC;AACjC,MAAM,oBAAoB;;;;AAuE1B,MAAM,gBAAgB;;IAClB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;QAClD,cAAc;QACd,UAAU;QACV,OAAO;YAAC;gBAAE,aAAa;gBAAI,UAAU;gBAAG,WAAW;gBAAG,OAAO;YAAE;SAAE;QACjE,UAAU;QACV,YAAY;QACZ,SAAS;QACT,eAAe;QACf,MAAM,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC5C,iBAAiB;IACrB;IACA,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IACtE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IAC/D,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAE1C,yDAAyD;IACzD,MAAM,iBAAiB;QACnB,IAAI;YACA,eAAe;YACf,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,gBAAgB,yHAAA,CAAA,iBAAc,CAAC,UAAU;YACrD,QAAQ,GAAG,CAAC,kBAAkB;YAC9B,QAAQ,GAAG,CAAC,gBAAgB;YAE5B,mDAAmD;YACnD,IAAI;gBACA,QAAQ,GAAG,CAAC,uCAAuC;gBACnD,MAAM,aAAa,MAAM,yHAAA,CAAA,YAAS,CAAC,WAAW,CAC1C,yHAAA,CAAA,iBAAc,CAAC,UAAU,EACzB,uBACA;gBAGJ,QAAQ,GAAG,CAAC,mCAAmC;gBAC/C,QAAQ,GAAG,CAAC,wBAAwB,WAAW,SAAS;gBACxD,QAAQ,GAAG,CAAC,mBAAmB,OAAO,WAAW,SAAS;gBAE1D,iEAAiE;gBACjE,QAAQ,GAAG,CAAC,+BAA+B,WAAW,SAAS,GAAG,kBAAkB,WAAW,SAAS,GAAG;gBAE3G,sEAAsE;gBACtE,IAAI,YAAY,WAAW,SAAS,GAAG,SAAS,WAAW,SAAS,EAAE,MAAM;gBAC5E,QAAQ,GAAG,CAAC,+BAA+B;gBAE3C,iDAAiD;gBACjD,YAAY,MAAM,aAAa,OAAO;gBACtC,QAAQ,GAAG,CAAC,qCAAqC;gBAEjD,kDAAkD;gBAClD,MAAM,mBAAmB,CAAC,GAAG,EAAE,WAAW;gBAC1C,QAAQ,GAAG,CAAC,6BAA6B;gBAEzC,YAAY,CAAA,OAAQ,CAAC;wBACjB,GAAG,IAAI;wBACP,eAAe;wBACf,iBAAiB;oBACrB,CAAC;gBACD,QAAQ,GAAG,CAAC;gBACZ;YACJ,EAAE,OAAO,UAAU;gBACf,QAAQ,KAAK,CAAC,2CAA2C;gBACzD,QAAQ,GAAG,CAAC,kBAAkB,KAAK,SAAS,CAAC;gBAC7C,QAAQ,GAAG,CAAC;YAChB;YAEA,6CAA6C;YAC7C,2CAA2C;YAC3C,IAAI;gBACA,MAAM,YAAY;gBAClB,QAAQ,GAAG,CAAC,0DAA0D;gBACtE,QAAQ,GAAG,CAAC,gCAAgC;oBACxC,WAAW,UAAU,QAAQ;gBACjC;gBAEA,MAAM,iBAAiB,MAAM,yHAAA,CAAA,YAAS,CAAC,cAAc,CACjD,yHAAA,CAAA,iBAAc,CAAC,UAAU,EACzB,uBACA,oBACA;oBACI,WAAW,UAAU,QAAQ;gBACjC;gBAGJ,QAAQ,GAAG,CAAC,iCAAiC;gBAC7C,QAAQ,GAAG,CAAC,wBAAwB,eAAe,GAAG;gBACtD,QAAQ,GAAG,CAAC,+BAA+B,eAAe,SAAS;gBAEnE,qEAAqE;gBACrE,QAAQ,GAAG,CAAC,oCAAoC,eAAe,SAAS,GAAG,kBAAkB,eAAe,SAAS,GAAG;gBAExH,6CAA6C;gBAC7C,YAAY,CAAA,OAAQ,CAAC;wBACjB,GAAG,IAAI;wBACP,eAAe,CAAC,GAAG,EAAE,WAAW;wBAChC,iBAAiB;oBACrB,CAAC;gBACD,QAAQ,GAAG,CAAC,kDAAkD,CAAC,GAAG,EAAE,WAAW;gBAC/E;YACJ,EAAE,OAAO,aAAa;gBAClB,QAAQ,KAAK,CAAC,4CAA4C;gBAC1D,QAAQ,GAAG,CAAC,kBAAkB,KAAK,SAAS,CAAC,aAAa,MAAM;gBAChE,MAAM,aAAa,2CAA2C;YAClE;QACJ,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,mCAAmC;YACjD,QAAQ,GAAG,CAAC,eAAe,OAAO;YAClC,QAAQ,GAAG,CAAC,kBAAkB,KAAK,SAAS,CAAC,OAAO,MAAM;YAE1D,qDAAqD;YACrD,MAAM,YAAY,IAAI,OAAO,OAAO,GAAG,QAAQ,GAAG,KAAK,CAAC,CAAC;YACzD,QAAQ,GAAG,CAAC,sCAAsC;YAElD,YAAY,CAAA,OAAQ,CAAC;oBACjB,GAAG,IAAI;oBACP,eAAe,CAAC,GAAG,EAAE,WAAW;gBACpC,CAAC;YACD,QAAQ,GAAG,CAAC,mDAAmD,CAAC,GAAG,EAAE,WAAW;YAEhF,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QAChB,SAAU;YACN,eAAe;YACf,QAAQ,GAAG,CAAC;QAChB;IACJ;IAEA,gDAAgD;IAChD,MAAM,kBAAkB,OAAO;QAC3B,IAAI;YACA,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,uCAAuC;YAEnD,qCAAqC;YACrC,IAAI;gBACA,MAAM,cAAc,MAAM,yHAAA,CAAA,YAAS,CAAC,WAAW,CAC3C,yHAAA,CAAA,iBAAc,CAAC,UAAU,EACzB,uBACA;gBAGJ,QAAQ,GAAG,CAAC,4CAA4C;gBAExD,0BAA0B;gBAC1B,QAAQ,GAAG,CAAC,yCAAyC,MAAM,QAAQ;gBACnE,MAAM,iBAAiB,MAAM,yHAAA,CAAA,YAAS,CAAC,cAAc,CACjD,yHAAA,CAAA,iBAAc,CAAC,UAAU,EACzB,uBACA,oBACA;oBACI,WAAW,MAAM,QAAQ;gBAC7B;gBAGJ,QAAQ,GAAG,CAAC,sBAAsB;gBAClC,QAAQ,GAAG,CAAC,qCAAqC,eAAe,SAAS;gBACzE,OAAO;YACX,EAAE,OAAO,UAAU;gBACf,QAAQ,KAAK,CAAC,yCAAyC;gBAEvD,uCAAuC;gBACvC,QAAQ,GAAG,CAAC,kDAAkD,MAAM,QAAQ;gBAC5E,MAAM,iBAAiB,MAAM,yHAAA,CAAA,YAAS,CAAC,cAAc,CACjD,yHAAA,CAAA,iBAAc,CAAC,UAAU,EACzB,uBACA,oBACA;oBACI,WAAW,MAAM,QAAQ;gBAC7B;gBAGJ,QAAQ,GAAG,CAAC,iCAAiC;gBAC7C,QAAQ,GAAG,CAAC,qCAAqC,eAAe,SAAS;gBACzE,OAAO;YACX;QACJ,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,QAAQ,GAAG,CAAC,kBAAkB,KAAK,SAAS,CAAC,OAAO,MAAM;YAC1D,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACZ,OAAO;QACX;IACJ;IAEA,wCAAwC;IACxC,MAAM,uBAAuB;QACzB,IAAI;YACA,eAAe;YAEf,IAAI,YAAY;YAEhB,4BAA4B;YAC5B,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,uCAAuC;YAEnD,IAAI;gBACA,MAAM,WAAW,MAAM,yHAAA,CAAA,YAAS,CAAC,WAAW,CACxC,yHAAA,CAAA,iBAAc,CAAC,UAAU,EACzB,uBACA;gBAGJ,QAAQ,GAAG,CAAC,yBAAyB;gBACrC,QAAQ,GAAG,CAAC,mCAAmC,SAAS,SAAS;gBACjE,QAAQ,GAAG,CAAC,mBAAmB,OAAO,SAAS,SAAS;gBAExD,yFAAyF;gBACzF,QAAQ,GAAG,CAAC,sDACR,SAAS,SAAS,GAAG,kBAAkB,SAAS,SAAS,GAAG;gBAEhE,0EAA0E;gBAC1E,MAAM,WAAW,SAAS,SAAS,GAAG,SAAS,SAAS,SAAS,EAAE,MAAM;gBACzE,QAAQ,GAAG,CAAC,+BAA+B;gBAE3C,iDAAiD;gBACjD,YAAY,MAAM,YAAY,OAAO;gBACrC,QAAQ,GAAG,CAAC,qCAAqC;YACrD,EAAE,OAAO,UAAU;gBACf,QAAQ,KAAK,CAAC,iDAAiD;gBAC/D,QAAQ,GAAG,CAAC,kBAAkB,KAAK,SAAS,CAAC,UAAU,MAAM;YAC7D,wDAAwD;YAC5D;YAEA,mBAAmB;YACnB,MAAM,QAAQ,YAAY;YAC1B,QAAQ,GAAG,CAAC,2BAA2B;YAEvC,4BAA4B;YAC5B,QAAQ,GAAG,CAAC,uCAAuC;YACnD,MAAM,UAAU,MAAM,gBAAgB;YACtC,QAAQ,GAAG,CAAC,kBAAkB;YAE9B,IAAI,SAAS;gBACT,wCAAwC;gBACxC,MAAM,mBAAmB,CAAC,GAAG,EAAE,OAAO;gBACtC,QAAQ,GAAG,CAAC,+BAA+B;gBAE3C,YAAY,CAAA,OAAQ,CAAC;wBACjB,GAAG,IAAI;wBACP,eAAe;wBACf,iBAAiB;oBACrB,CAAC;gBACD,QAAQ,GAAG,CAAC;gBAEZ,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;YAClB,OAAO;gBACH,QAAQ,GAAG,CAAC;gBACZ,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YAChB;QACJ,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,oCAAoC;YAClD,QAAQ,GAAG,CAAC,kBAAkB,KAAK,SAAS,CAAC,OAAO,MAAM;YAE1D,4CAA4C;YAC5C,MAAM,YAAY,IAAI,OAAO,OAAO,GAAG,QAAQ,GAAG,KAAK,CAAC,CAAC;YACzD,MAAM,aAAa,CAAC,GAAG,EAAE,WAAW;YACpC,QAAQ,GAAG,CAAC,sCAAsC;YAElD,YAAY,CAAA,OAAQ,CAAC;oBACjB,GAAG,IAAI;oBACP,eAAe;gBACnB,CAAC;YACD,QAAQ,GAAG,CAAC;YAEZ,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QAChB,SAAU;YACN,eAAe;YACf,QAAQ,GAAG,CAAC;QAChB;IACJ;IAEA,uCAAuC;IACvC,MAAM,sBAAsB;QACxB,IAAI,qBAAqB,MAAM,MAAM,OAAO,oBAAoB;YAC5D,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACZ;QACJ;QAEA,MAAM,QAAQ,OAAO;QACrB,IAAI,QAAQ,MAAM;YACd,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACZ;QACJ;QAEA,IAAI;YACA,eAAe;YACf,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,iCAAiC;YAE7C,4BAA4B;YAC5B,MAAM,UAAU,MAAM,gBAAgB;YACtC,QAAQ,GAAG,CAAC,kBAAkB;YAE9B,IAAI,SAAS;gBACT,wCAAwC;gBACxC,MAAM,mBAAmB,CAAC,GAAG,EAAE,OAAO;gBACtC,QAAQ,GAAG,CAAC,+BAA+B;gBAE3C,YAAY,CAAA,OAAQ,CAAC;wBACjB,GAAG,IAAI;wBACP,eAAe;wBACf,iBAAiB;oBACrB,CAAC;gBACD,QAAQ,GAAG,CAAC;gBAEZ,oBAAoB;gBACpB,kBAAkB;gBAClB,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC,CAAC,kBAAkB,EAAE,OAAO;YAC9C,OAAO;gBACH,QAAQ,GAAG,CAAC;gBACZ,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YAChB;QACJ,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,oCAAoC;YAClD,QAAQ,GAAG,CAAC,kBAAkB,KAAK,SAAS,CAAC,OAAO,MAAM;YAE1D,oDAAoD;YACpD,MAAM,aAAa,CAAC,GAAG,EAAE,OAAO;YAChC,QAAQ,GAAG,CAAC,6BAA6B;YAEzC,YAAY,CAAA,OAAQ,CAAC;oBACjB,GAAG,IAAI;oBACP,eAAe;oBACf,iBAAiB;gBACrB,CAAC;YACD,QAAQ,GAAG,CAAC;YAEZ,oBAAoB;YACpB,kBAAkB;YAClB,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QAChB,SAAU;YACN,eAAe;YACf,QAAQ,GAAG,CAAC;QAChB;IACJ;IAEA,4DAA4D;IAC5D,MAAM,sBAAsB;QACxB,IAAI;YACA,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC;YAEZ,IAAI;gBACA,mCAAmC;gBACnC,MAAM,cAAc,MAAM,yHAAA,CAAA,YAAS,CAAC,WAAW,CAC3C,yHAAA,CAAA,iBAAc,CAAC,UAAU,EACzB,uBACA;gBAGJ,QAAQ,GAAG,CAAC,oCAAoC;gBAChD,QAAQ,GAAG,CAAC,sBAAsB,YAAY,SAAS;gBAEvD,8BAA8B;gBAC9B;YACJ,EAAE,OAAM;gBACJ,QAAQ,GAAG,CAAC;gBAEZ,2CAA2C;gBAC3C,MAAM,YAAY;gBAClB,MAAM,yHAAA,CAAA,YAAS,CAAC,cAAc,CAC1B,yHAAA,CAAA,iBAAc,CAAC,UAAU,EACzB,uBACA,oBACA;oBACI,WAAW,UAAU,QAAQ;gBACjC;gBAGJ,QAAQ,GAAG,CAAC,qDAAqD;gBAEjE,eAAe;gBACf;YACJ;QACJ,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,kCAAkC;YAChD,gDAAgD;YAChD;QACJ;IACJ;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACN,2CAA2C;YAC3C,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,gBAAgB,yHAAA,CAAA,iBAAc,CAAC,UAAU;YACrD,QAAQ,GAAG,CAAC,2BAA2B,yHAAA,CAAA,iBAAc,CAAC,oBAAoB;YAC1E,QAAQ,GAAG,CAAC,eAAe,yHAAA,CAAA,iBAAc,CAAC,SAAS;YACnD,QAAQ,GAAG,CAAC,uBAAuB;YACnC,QAAQ,GAAG,CAAC,0BAA0B;YAEtC,sDAAsD;YACtD;QACA,uDAAuD;QAC3D;kCAAG,EAAE;IAEL,yEAAyE;IAEzE,MAAM,mBAAmB,CAAC,OAAe,OAAuB;QAC5D,mCAAmC;QACnC,MAAM,WAAW;eAAI,SAAS,KAAK;SAAC;QAEpC,4BAA4B;QAC5B,QAAQ,CAAC,MAAM,GAAG;YACd,GAAG,QAAQ,CAAC,MAAM;YAClB,CAAC,MAAM,EAAE;QACb;QAEA,gDAAgD;QAChD,IAAI,UAAU,cAAc,UAAU,aAAa;YAC/C,QAAQ,CAAC,MAAM,CAAC,KAAK,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,QAAQ,IAAI,OAAO,QAAQ,CAAC,MAAM,CAAC,SAAS;QAC/F;QAEA,0CAA0C;QAC1C,YAAY,CAAA;YACR,4CAA4C;YAC5C,MAAM,WAAW,SAAS,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,KAAK,EAAE;YAElE,0EAA0E;YAC1E,MAAM,UAAU,WAAW,KAAK,UAAU;YAE1C,iEAAiE;YACjE,OAAO;gBACH,GAAG,IAAI;gBACP,OAAO;gBACP;gBACA;YACJ;QACJ;IACJ;IAEA,MAAM,cAAc;QAChB,YAAY,CAAA;YACR,qBAAqB;YACrB,MAAM,WAAW;mBAAI,KAAK,KAAK;gBAAE;oBAAE,aAAa;oBAAI,UAAU;oBAAG,WAAW;oBAAG,OAAO;gBAAE;aAAE;YAE1F,+DAA+D;YAC/D,yCAAyC;YACzC,MAAM,WAAW,SAAS,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,KAAK,EAAE;YAClE,MAAM,UAAU,WAAW,KAAK,UAAU;YAE1C,OAAO;gBACH,GAAG,IAAI;gBACP,OAAO;gBACP;gBACA;YACJ;QACJ;IACJ;IAEA,MAAM,iBAAiB,CAAC;QACpB,IAAI,SAAS,KAAK,CAAC,MAAM,GAAG,GAAG;YAC3B,MAAM,WAAW,SAAS,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;YAEvD,0CAA0C;YAC1C,YAAY,CAAA;gBACR,iDAAiD;gBACjD,MAAM,WAAW,SAAS,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,KAAK,EAAE;gBAElE,wBAAwB;gBACxB,MAAM,UAAU,WAAW,KAAK,UAAU;gBAE1C,OAAO;oBACH,GAAG,IAAI;oBACP,OAAO;oBACP;oBACA;gBACJ;YACJ;QACJ;IACJ;IAEA,MAAM,eAAe,CAAC;QAClB,EAAE,cAAc;QAChB,iBAAiB;IACrB;IAEA,gCAAgC;IAChC,MAAM,sBAAsB,CAAC;QACzB,eAAe;QACf,iBAAiB;QACjB,eAAe;IACnB;IAEA,MAAM,eAAe,CAAC;QAClB,YAAY;YACR,GAAG,QAAQ;YACX,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK;QACnC;IACJ;IAEA,MAAM,cAAc;QAChB,IAAI;YACA,IAAI,OAAO,OAAO,QAAQ,KAAK,cAAc,WAAW,OAAO,EAAE;gBAC7D,yCAAyC;gBACzC,0JAAA,CAAA,UAAK,CAAC,OAAO,eACT,6LAAC;oBAAI,WAAU;;sCACX,6LAAC,8IAAA,CAAA,UAAc;4BAAC,MAAK;;;;;;sCACrB,6LAAC;sCAAK;;;;;;;;;;;;gBAId,+DAA+D;gBAC/D,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBAEjD,MAAM,UAAU,WAAW,OAAO;gBAClC,MAAM,WAAW,GAAG,SAAS,aAAa,CAAC,CAAC,EAAE,SAAS,YAAY,CAAC,OAAO,CAAC,QAAQ,KAAK,IAAI,CAAC;gBAE9F,QAAQ,GAAG,CAAC;gBACZ,QAAQ,GAAG,CAAC,cAAc;gBAE1B,yDAAyD;gBACzD,8CAA8C;gBAC9C,IAAI;oBACA,8CAA8C;oBAC9C,MAAM,MAAuB,gBAAgB,SAAS;wBAClD,QAAQ;4BAAC;4BAAG;4BAAG;4BAAG;yBAAE;wBACpB,UAAU;wBACV,OAAO;4BAAE,MAAM;4BAAQ,SAAS;wBAAK;wBACrC,aAAa;4BACT,OAAO;4BACP,SAAS;4BACT,SAAS;4BACT,iBAAiB;4BACjB,YAAY;4BACZ,wBAAwB;4BACxB,SAAS;4BACT,cAAc,OAAO,WAAW,GAAG;wBACvC;wBACA,OAAO;4BACH,MAAM;4BACN,QAAQ;gCAAC;gCAAI;6BAAI;4BACjB,aAAa;4BACb,UAAU;wBACd;oBACJ,IAAI;wBACA,QAAQ;4BAAC;4BAAG;4BAAG;4BAAG;yBAAE;wBACpB,UAAU;wBACV,OAAO;4BAAE,MAAM;4BAAQ,SAAS;wBAAK;wBACrC,aAAa;4BACT,OAAO;4BACP,SAAS;4BACT,SAAS;4BACT,iBAAiB;4BACjB,YAAY;4BACZ,wBAAwB;4BACxB,SAAS;4BACT,cAAc,OAAO,WAAW,GAAG;wBACvC;wBACA,OAAO;4BACH,MAAM;4BACN,QAAQ;4BACR,aAAa;4BACb,UAAU;wBACd;oBACJ;oBAEA,oCAAoC;oBACpC,MAAM,SAAS,KAAK,CAAC,KAAK;oBAE1B,8BAA8B;oBAC9B,MAAM,SAAS,MAAM,IAAI,CAAC,QAAQ,oBAAoB,CAAC;oBACvD,MAAM,QAAQ,GAAG,CAAC,OAAO,GAAG,CAAC,CAAA;wBACzB,IAAI,IAAI,QAAQ,EAAE,OAAO,QAAQ,OAAO;wBACxC,OAAO,IAAI,QAAQ,CAAA;4BACf,IAAI,MAAM,GAAG;4BACb,IAAI,OAAO,GAAG;wBAClB;oBACJ;oBAEA,uDAAuD;oBACvD,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;oBAEjD,iDAAiD;oBACjD,MAAM,YAAY,MAAM,OAAO,QAAQ,GAClC,GAAG,CAAC,KACJ,IAAI,CAAC,SACL,MAAM,CAAC;oBAEZ,QAAQ,GAAG,CAAC;oBAEZ,gCAAgC;oBAChC,MAAM,OAAO,SAAS,aAAa,CAAC;oBACpC,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC,IAAI,KAAK;wBAAC;qBAAU,EAAE;wBAAE,MAAM;oBAAkB;oBACvF,KAAK,IAAI,GAAG;oBACZ,KAAK,QAAQ,GAAG;oBAEhB,8DAA8D;oBAC9D,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;oBACjD,KAAK,KAAK;oBAEV,8BAA8B;oBAC9B,WAAW;wBACP,OAAO,GAAG,CAAC,eAAe,CAAC;oBAC/B,GAAG;oBAEH,uCAAuC;oBACvC,IAAI;wBACA,QAAQ,GAAG,CAAC;wBAEZ,qBAAqB;wBACrB,MAAM,OAAO,IAAI,KAAK;4BAAC;yBAAU,EAAE,UAAU;4BACzC,MAAM;wBACV;wBAEA,4BAA4B;wBAC5B,MAAM,SAAS,iJAAA,CAAA,KAAE,CAAC,MAAM;wBACxB,QAAQ,GAAG,CAAC,YAAY;wBAExB,iDAAiD;wBACjD,MAAM,eAAe,MAAM,yHAAA,CAAA,UAAO,CAAC,UAAU,CACzC,yHAAA,CAAA,iBAAc,CAAC,SAAS,EACxB,QACA,MACA;4BAAC;yBAAc,CAAC,wCAAwC;;wBAE5D,QAAQ,GAAG,CAAC,+BAA+B;wBAE3C,4BAA4B;wBAC5B,MAAM,UAAU,yHAAA,CAAA,UAAO,CAAC,eAAe,CACnC,yHAAA,CAAA,iBAAc,CAAC,SAAS,EACxB,aAAa,GAAG,EAClB,QAAQ;wBACV,QAAQ,GAAG,CAAC,aAAa;wBAEzB,gFAAgF;wBAChF,MAAM,cAAc,MAAM,yHAAA,CAAA,YAAS,CAAC,cAAc,CAC9C,yHAAA,CAAA,iBAAc,CAAC,UAAU,EACzB,mBACA,iJAAA,CAAA,KAAE,CAAC,MAAM,IACT;4BACI,MAAM,SAAS,YAAY;4BAC3B,WAAW,SAAS,aAAa;4BACjC,YAAY;wBAChB;wBAEJ,QAAQ,GAAG,CAAC,qCAAqC;wBAEjD,mDAAmD;wBACnD,MAAM;oBACV,EAAE,OAAO,aAAa;wBAClB,QAAQ,KAAK,CAAC,oCAAoC;wBAClD,QAAQ,GAAG,CAAC,kBAAkB,KAAK,SAAS,CAAC,aAAa,MAAM;oBAChE,iEAAiE;oBACrE;oBAEA,yCAAyC;oBACzC,0JAAA,CAAA,UAAK,CAAC,OAAO;oBACb,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;oBAEd,OAAO;gBACX,EAAE,OAAO,OAAO;oBACZ,QAAQ,KAAK,CAAC,yBAAyB;oBACvC,QAAQ,GAAG,CAAC,kBAAkB,KAAK,SAAS,CAAC,OAAO,MAAM;oBAC1D,0JAAA,CAAA,UAAK,CAAC,OAAO;oBACb,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;oBACZ,MAAM;gBACV;YACJ;YACA,MAAM,IAAI,MAAM;QACpB,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,yBAAyB;YACvC,QAAQ,GAAG,CAAC,kBAAkB,KAAK,SAAS,CAAC,OAAO,MAAM;YAC1D,0JAAA,CAAA,UAAK,CAAC,OAAO;YACb,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACZ,MAAM;QACV;IACJ;IAEA,gEAAgE;IAChE,MAAM,yBAAyB,OAAO;QAClC,IAAI;YACA,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,+BAA+B;YAE3C,8CAA8C;YAC9C,MAAM,WAAW,MAAM,yHAAA,CAAA,YAAS,CAAC,aAAa,CAC1C,yHAAA,CAAA,iBAAc,CAAC,UAAU,EACzB,mBACA;gBAAC,iJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,aAAa;aAAe;YAG7C,QAAQ,GAAG,CAAC,mBAAmB;YAE/B,IAAI,SAAS,SAAS,CAAC,MAAM,GAAG,GAAG;gBAC/B,6BAA6B;gBAC7B,MAAM,cAAc,SAAS,SAAS,CAAC,EAAE;gBACzC,QAAQ,GAAG,CAAC,+BAA+B;gBAC3C,OAAO;oBACH,QAAQ;oBACR,SAAS,YAAY,UAAU;oBAC/B,UAAU;gBACd;YACJ;YAEA,gCAAgC;YAChC,QAAQ,GAAG,CAAC;YACZ,OAAO;gBAAE,QAAQ;YAAM;QAC3B,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,4CAA4C;YAC1D,QAAQ,GAAG,CAAC,kBAAkB,KAAK,SAAS,CAAC,OAAO,MAAM;YAC1D,OAAO;gBAAE,QAAQ;gBAAO;YAAM;QAClC;IACJ;IAEA,2DAA2D;IAC3D,MAAM,gCAAgC;QAClC,IAAI;YACA,QAAQ,GAAG,CAAC;YAEZ,6BAA6B;YAC7B,MAAM,kBAAkB,SAAS,eAAe,IAAI;YACpD,MAAM,QAAQ,kBAAkB;YAChC,QAAQ,GAAG,CAAC,eAAe;YAC3B,QAAQ,GAAG,CAAC,WAAW;YAEvB,4BAA4B;YAC5B,MAAM,UAAU,MAAM,gBAAgB;YAEtC,IAAI,SAAS;gBACT,QAAQ,GAAG,CAAC;gBAEZ,+CAA+C;gBAC/C,YAAY,CAAA,OAAQ,CAAC;wBACjB,GAAG,IAAI;wBACP,eAAe,CAAC,GAAG,EAAE,OAAO;wBAC5B,iBAAiB;oBACrB,CAAC;gBAED,QAAQ,GAAG,CAAC;gBACZ,OAAO;YACX,OAAO;gBACH,QAAQ,KAAK,CAAC;gBACd,OAAO;YACX;QACJ,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,kCAAkC;YAChD,QAAQ,GAAG,CAAC,kBAAkB,KAAK,SAAS,CAAC,OAAO,MAAM;YAC1D,OAAO;QACX;IACJ;IAEA,gEAAgE;IAChE,MAAM,sBAAsB;QACxB,IAAI;YACA,QAAQ,GAAG,CAAC;YAEZ,IAAI,OAAO,OAAO,QAAQ,KAAK,cAAc,WAAW,OAAO,EAAE;gBAC7D,MAAM,UAAU,WAAW,OAAO;gBAClC,MAAM,WAAW,GAAG,SAAS,aAAa,CAAC,CAAC,EAAE,SAAS,YAAY,CAAC,OAAO,CAAC,QAAQ,KAAK,IAAI,CAAC;gBAE9F,QAAQ,GAAG,CAAC,4BAA4B;gBAExC,gDAAgD;gBAChD,MAAM,MAAuB,gBAAgB,SAAS;oBAClD,QAAQ;wBAAC;wBAAG;wBAAG;wBAAG;qBAAE;oBACpB,UAAU;oBACV,OAAO;wBAAE,MAAM;wBAAQ,SAAS;oBAAK;oBACrC,aAAa;wBACT,OAAO;wBACP,SAAS;wBACT,SAAS;wBACT,iBAAiB;wBACjB,YAAY;wBACZ,wBAAwB;wBACxB,SAAS;wBACT,cAAc,OAAO,WAAW,GAAG;oBACvC;oBACA,OAAO;wBACH,MAAM;wBACN,QAAQ;4BAAC;4BAAI;yBAAI;wBACjB,aAAa;wBACb,UAAU;oBACd;gBACJ,IAAI;oBACA,QAAQ;wBAAC;wBAAG;wBAAG;wBAAG;qBAAE;oBACpB,UAAU;oBACV,OAAO;wBAAE,MAAM;wBAAQ,SAAS;oBAAK;oBACrC,aAAa;wBACT,OAAO;wBACP,SAAS;wBACT,SAAS;wBACT,iBAAiB;wBACjB,YAAY;wBACZ,wBAAwB;wBACxB,SAAS;wBACT,cAAc,OAAO,WAAW,GAAG;oBACvC;oBACA,OAAO;wBACH,MAAM;wBACN,QAAQ;wBACR,aAAa;wBACb,UAAU;oBACd;gBACJ;gBAEA,oCAAoC;gBACpC,MAAM,SAAS,KAAK,CAAC,KAAK;gBAE1B,8BAA8B;gBAC9B,MAAM,SAAS,MAAM,IAAI,CAAC,QAAQ,oBAAoB,CAAC;gBACvD,MAAM,QAAQ,GAAG,CAAC,OAAO,GAAG,CAAC,CAAA;oBACzB,IAAI,IAAI,QAAQ,EAAE,OAAO,QAAQ,OAAO;oBACxC,OAAO,IAAI,QAAQ,CAAA;wBACf,IAAI,MAAM,GAAG;wBACb,IAAI,OAAO,GAAG;oBAClB;gBACJ;gBAEA,uDAAuD;gBACvD,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBAEjD,oBAAoB;gBACpB,MAAM,UAAU,MAAM,OAAO,QAAQ,GAChC,GAAG,CAAC,KACJ,IAAI,CAAC,SACL,MAAM,CAAC;gBAEZ,QAAQ,GAAG,CAAC;gBACZ,OAAO;YACX;YAEA,MAAM,IAAI,MAAM;QACpB,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,QAAQ,GAAG,CAAC,kBAAkB,KAAK,SAAS,CAAC,OAAO,MAAM;YAC1D,MAAM;QACV;IACJ;IAEA,6DAA6D;IAC7D,MAAM,sBAAsB,OAAO;QAC/B,IAAI;YACA,QAAQ,GAAG,CAAC;YAEZ,qBAAqB;YACrB,MAAM,WAAW,GAAG,SAAS,aAAa,CAAC,CAAC,EAAE,SAAS,YAAY,CAAC,OAAO,CAAC,QAAQ,KAAK,IAAI,CAAC;YAC9F,MAAM,OAAO,IAAI,KAAK;gBAAC;aAAQ,EAAE,UAAU;gBACvC,MAAM;YACV;YAEA,QAAQ,GAAG,CAAC,cAAc;YAE1B,4BAA4B;YAC5B,MAAM,SAAS,iJAAA,CAAA,KAAE,CAAC,MAAM;YACxB,QAAQ,GAAG,CAAC,YAAY;YAExB,iDAAiD;YACjD,MAAM,eAAe,MAAM,yHAAA,CAAA,UAAO,CAAC,UAAU,CACzC,yHAAA,CAAA,iBAAc,CAAC,SAAS,EACxB,QACA,MACA;gBAAC;aAAc,CAAC,wCAAwC;;YAE5D,QAAQ,GAAG,CAAC,+BAA+B;YAE3C,4BAA4B;YAC5B,MAAM,UAAU,yHAAA,CAAA,UAAO,CAAC,eAAe,CACnC,yHAAA,CAAA,iBAAc,CAAC,SAAS,EACxB,aAAa,GAAG,EAClB,QAAQ;YACV,QAAQ,GAAG,CAAC,aAAa;YAEzB,gFAAgF;YAChF,MAAM,cAAc,MAAM,yHAAA,CAAA,YAAS,CAAC,cAAc,CAC9C,yHAAA,CAAA,iBAAc,CAAC,UAAU,EACzB,mBACA,iJAAA,CAAA,KAAE,CAAC,MAAM,IACT;gBACI,MAAM,SAAS,YAAY;gBAC3B,WAAW,SAAS,aAAa;gBACjC,YAAY;YAChB;YAEJ,QAAQ,GAAG,CAAC,qCAAqC;YAEjD,mDAAmD;YACnD,MAAM;YAEN,OAAO;QACX,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,oCAAoC;YAClD,QAAQ,GAAG,CAAC,kBAAkB,KAAK,SAAS,CAAC,OAAO,MAAM;YAC1D,MAAM;QACV;IACJ;IAEA,MAAM,eAAe;QACjB,IAAI;YACA,0JAAA,CAAA,UAAK,CAAC,OAAO,eACT,6LAAC;gBAAI,WAAU;;kCACX,6LAAC,8IAAA,CAAA,UAAc;wBAAC,MAAK;;;;;;kCACrB,6LAAC;kCAAK;;;;;;;;;;;;YAId,IAAI,UAAU;YAEd,6DAA6D;YAC7D,MAAM,cAAc,MAAM,uBAAuB,SAAS,aAAa;YAEvE,IAAI,YAAY,MAAM,IAAI,YAAY,OAAO,EAAE;gBAC3C,uBAAuB;gBACvB,QAAQ,GAAG,CAAC,gCAAgC,YAAY,OAAO;gBAC/D,UAAU,YAAY,OAAO;gBAE7B,oEAAoE;gBACpE,QAAQ,GAAG,CAAC;YAChB,OAAO;gBACH,uDAAuD;gBACvD,QAAQ,GAAG,CAAC;gBAEZ,IAAI;oBACA,2CAA2C;oBAC3C,MAAM,UAAU,MAAM;oBAEtB,6BAA6B;oBAC7B,UAAU,MAAM,oBAAoB;gBACxC,EAAE,OAAO,OAAO;oBACZ,QAAQ,KAAK,CAAC,sCAAsC;oBACpD,0JAAA,CAAA,UAAK,CAAC,OAAO;oBACb,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;oBACZ;gBACJ;YACJ;YAEA,qBAAqB;YACrB,MAAM,UAAU,mBACZ,CAAC,MAAM,EAAE,SAAS,YAAY,CAAC,sBAAsB,EAAE,SAAS,aAAa,CAAC,MAAM,EAAE,SAAS,QAAQ,CAAC,cAAc,GAAG,uDAAuD,EAAE,SAAS;YAG/L,6BAA6B;YAC7B,IAAI,QAAQ,SAAS,QAAQ,CAAC,OAAO,CAAC,WAAW;YAEjD,kCAAkC;YAClC,IAAI,CAAC,MAAM,UAAU,CAAC,UAAU,CAAC,MAAM,UAAU,CAAC,SAAS;gBACvD,mDAAmD;gBACnD,IAAI,MAAM,UAAU,CAAC,MAAM;oBACvB,QAAQ,QAAQ,MAAM,SAAS,CAAC;gBACpC,OAAO;oBACH,iCAAiC;oBACjC,QAAQ,QAAQ;gBACpB;YACJ;YAEA,sBAAsB;YACtB,QAAQ,MAAM,OAAO,CAAC,KAAK;YAE3B,QAAQ,GAAG,CAAC,8BAA8B;YAE1C,sBAAsB;YACtB,MAAM,cAAc,CAAC,cAAc,EAAE,MAAM,MAAM,EAAE,SAAS;YAC5D,QAAQ,GAAG,CAAC,iBAAiB;YAE7B,wBAAwB;YACxB,0JAAA,CAAA,UAAK,CAAC,OAAO;YAEb,6BAA6B;YAC7B,OAAO,IAAI,CAAC,aAAa;YAEzB,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QAClB,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,0BAA0B;YACxC,QAAQ,GAAG,CAAC,kBAAkB,KAAK,SAAS,CAAC,OAAO,MAAM;YAC1D,0JAAA,CAAA,UAAK,CAAC,OAAO;YACb,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QAChB;IACJ;IAIA,MAAM,YAAY;QACd,4BAA4B;QAC5B,IAAI;YACA,eAAe;YACf,QAAQ,GAAG,CAAC;YAEZ,IAAI,YAAY;YAChB,IAAI,QAAQ;YAEZ,4BAA4B;YAC5B,QAAQ,GAAG,CAAC,uCAAuC;YAEnD,IAAI;gBACA,MAAM,WAAW,MAAM,yHAAA,CAAA,YAAS,CAAC,WAAW,CACxC,yHAAA,CAAA,iBAAc,CAAC,UAAU,EACzB,uBACA;gBAGJ,QAAQ,GAAG,CAAC,sCAAsC;gBAClD,QAAQ,GAAG,CAAC,mCAAmC,SAAS,SAAS;gBACjE,QAAQ,GAAG,CAAC,mBAAmB,OAAO,SAAS,SAAS;gBAExD,8EAA8E;gBAC9E,QAAQ,GAAG,CAAC,2CACR,SAAS,SAAS,GAAG,kBAAkB,SAAS,SAAS,GAAG;gBAEhE,0EAA0E;gBAC1E,MAAM,WAAW,SAAS,SAAS,GAAG,SAAS,SAAS,SAAS,EAAE,MAAM;gBACzE,QAAQ,GAAG,CAAC,+BAA+B;gBAE3C,iDAAiD;gBACjD,YAAY,MAAM,YAAY,OAAO;gBACrC,QAAQ,GAAG,CAAC,qCAAqC;gBAEjD,mBAAmB;gBACnB,QAAQ,YAAY;gBACpB,QAAQ,GAAG,CAAC,2BAA2B;YAC3C,EAAE,OAAO,UAAU;gBACf,QAAQ,KAAK,CAAC,iDAAiD;gBAC/D,QAAQ,GAAG,CAAC,kBAAkB,KAAK,SAAS,CAAC,UAAU,MAAM;gBAC7D,QAAQ,GAAG,CAAC;YACZ,0DAA0D;YAC9D;YAEA,mCAAmC;YACnC,IAAI;gBACA,QAAQ,GAAG,CAAC,uCAAuC;gBACnD,MAAM,UAAU,MAAM,gBAAgB;gBACtC,QAAQ,GAAG,CAAC,kBAAkB;YAClC,EAAE,OAAO,aAAa;gBAClB,QAAQ,KAAK,CAAC,8BAA8B;gBAC5C,QAAQ,GAAG,CAAC,kBAAkB,KAAK,SAAS,CAAC,aAAa,MAAM;gBAChE,QAAQ,GAAG,CAAC;YACZ,gDAAgD;YACpD;YAEA,yCAAyC;YACzC,QAAQ,GAAG,CAAC,uCAAuC;YACnD,YAAY;gBACR,cAAc;gBACd,UAAU;gBACV,OAAO;oBAAC;wBAAE,aAAa;wBAAI,UAAU;wBAAG,WAAW;wBAAG,OAAO;oBAAE;iBAAE;gBACjE,UAAU;gBACV,YAAY;gBACZ,SAAS;gBACT,eAAe,CAAC,GAAG,EAAE,OAAO;gBAC5B,iBAAiB;gBACjB,MAAM,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YAChD;YACA,QAAQ,GAAG,CAAC,uCAAuC,CAAC,GAAG,EAAE,OAAO;QACpE,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,oCAAoC;YAClD,QAAQ,GAAG,CAAC,kBAAkB,KAAK,SAAS,CAAC,OAAO,MAAM;YAE1D,iCAAiC;YACjC,MAAM,YAAY,IAAI,OAAO,OAAO,GAAG,QAAQ,GAAG,KAAK,CAAC,CAAC;YACzD,QAAQ,GAAG,CAAC,sCAAsC;YAElD,YAAY;gBACR,cAAc;gBACd,UAAU;gBACV,OAAO;oBAAC;wBAAE,aAAa;wBAAI,UAAU;wBAAG,WAAW;wBAAG,OAAO;oBAAE;iBAAE;gBACjE,UAAU;gBACV,YAAY;gBACZ,SAAS;gBACT,eAAe,CAAC,GAAG,EAAE,WAAW;gBAChC,MAAM,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YAChD;YACA,QAAQ,GAAG,CAAC,4CAA4C,CAAC,GAAG,EAAE,WAAW;QAC7E,SAAU;YACN,eAAe;YACf,eAAe;YACf,QAAQ,GAAG,CAAC;QAChB;IACJ;IAEA,MAAM,kBAAkB;QACpB,IAAI,OAAO,OAAO,CAAC,mFAAmF;YAClG;QACJ;IACJ;IAEA,qBACI,6LAAC;QAAI,WAAU;;0BACX,6LAAC,iIAAA,CAAA,UAAM;gBAAC,KAAI;;;;;;0BAEZ,6LAAC;gBAAI,WAAU;;kCAEX,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACP,YAAY;4BAAE,OAAO;wBAAK;wBAC1B,UAAU;4BAAE,OAAO;wBAAK;wBACxB,WAAU;kCAEV,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BACD,MAAK;4BACL,WAAU;;8CAEV,6LAAC;oCACG,OAAM;oCACN,WAAU;oCACV,MAAK;oCACL,SAAQ;oCACR,QAAO;8CAEP,cAAA,6LAAC;wCACG,eAAc;wCACd,gBAAe;wCACf,aAAa;wCACb,GAAE;;;;;;;;;;;gCAEJ;;;;;;;;;;;;kCAKd,6LAAC;wBAAI,WAAU;kCACX,cAAA,6LAAC;4BAAG,WAAU;sCAA0D;;;;;;;;;;;kCAG5E,6LAAC;wBAAI,WAAU;;0CACX,6LAAC;gCAAI,WAAU;0CACX,cAAA,6LAAC;oCAAK,UAAU;oCAAc,WAAU;;sDACpC,6LAAC;4CAAI,WAAU;;8DACX,6LAAC;;sEACG,6LAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAGhE,6LAAC;4DACG,MAAK;4DACL,MAAK;4DACL,OAAO,SAAS,YAAY;4DAC5B,UAAU;4DACV,QAAQ;4DACR,WAAU;;;;;;;;;;;;8DAIlB,6LAAC;;sEACG,6LAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAGhE,6LAAC;4DACG,MAAK;4DACL,MAAK;4DACL,OAAO,SAAS,IAAI;4DACpB,UAAU;4DACV,QAAQ;4DACR,WAAU;;;;;;;;;;;;8DAIlB,6LAAC;;sEACG,6LAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAGhE,6LAAC;4DACG,MAAK;4DACL,MAAK;4DACL,OAAO,SAAS,QAAQ;4DACxB,UAAU;4DACV,QAAQ;4DACR,aAAY;4DACZ,WAAU;;;;;;;;;;;;8DAIlB,6LAAC;;sEACG,6LAAC;4DAAI,WAAU;;8EACX,6LAAC;oEAAM,WAAU;8EAA0C;;;;;;8EAG3D,6LAAC;oEACG,MAAK;oEACL,SAAS,IAAM,kBAAkB,CAAC;oEAClC,WAAU;8EAET,iBAAiB,kBAAkB;;;;;;;;;;;;sEAI5C,6LAAC;4DAAI,WAAU;;8EACX,6LAAC;oEACG,MAAK;oEACL,MAAK;oEACL,OAAO,SAAS,aAAa;oEAC7B,QAAQ;oEACR,WAAU;;;;;;8EAEd,6LAAC;oEACG,MAAK;oEACL,SAAS;oEACT,UAAU;oEACV,WAAU;8EAET,cAAc,eAAe;;;;;;;;;;;;wDAIrC,gCACG,6LAAC;4DAAI,WAAU;;8EACX,6LAAC;oEAAE,WAAU;8EAA6B;;;;;;8EAC1C,6LAAC;oEAAI,WAAU;;sFACX,6LAAC;4EACG,MAAK;4EACL,OAAO;4EACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK,KAAK,KAAK,KAAK,OAAO,EAAE,MAAM,CAAC,KAAK;4EACvF,aAAY;4EACZ,KAAI;4EACJ,WAAU;;;;;;sFAEd,6LAAC;4EACG,MAAK;4EACL,SAAS;4EACT,UAAU;4EACV,WAAU;sFAET,4BACG,6LAAC;gFAAI,WAAU;;kGACX,6LAAC,8IAAA,CAAA,UAAc;wFAAC,MAAK;;;;;;kGACrB,6LAAC;kGAAK;;;;;;;;;;;uFAEV;;;;;;;;;;;;8EAGZ,6LAAC;oEAAE,WAAU;;wEAA6B;wEAAa,SAAS,eAAe,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;sDAMnG,6LAAC;4CAAI,WAAU;;8DACX,6LAAC;oDAAI,WAAU;;sEACX,6LAAC;4DAAG,WAAU;sEAAmD;;;;;;sEACjE,6LAAC;4DACG,MAAK;4DACL,SAAS;4DACT,WAAU;sEACb;;;;;;;;;;;;8DAKL,6LAAC;oDAAI,WAAU;8DACV,SAAS,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBACvB,6LAAC;4DAAgB,WAAU;;8EACvB,6LAAC;oEACG,MAAK;oEACL,aAAY;oEACZ,OAAO,KAAK,WAAW;oEACvB,UAAU,CAAC,IAAM,iBAAiB,OAAO,eAAe,EAAE,MAAM,CAAC,KAAK;oEACtE,WAAU;;;;;;8EAGd,6LAAC;oEACG,MAAK;oEACL,aAAY;oEACZ,OAAO,KAAK,QAAQ,KAAK,IAAI,KAAK,KAAK,QAAQ;oEAC/C,UAAU,CAAC;wEACP,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK,KAAK,KAAK,IAAI,OAAO,EAAE,MAAM,CAAC,KAAK;wEAC/D,iBAAiB,OAAO,YAAY;oEACxC;oEACA,SAAS,CAAC;wEACN,IAAI,EAAE,MAAM,CAAC,KAAK,KAAK,OAAO,EAAE,MAAM,CAAC,KAAK,KAAK,KAAK;4EAClD,EAAE,MAAM,CAAC,MAAM;wEACnB;oEACJ;oEACA,WAAU;oEACV,KAAI;oEACJ,MAAK;oEACL,WAAU;;;;;;8EAEd,6LAAC;oEACG,MAAK;oEACL,aAAY;oEACZ,OAAO,KAAK,SAAS,KAAK,IAAI,KAAK,KAAK,SAAS;oEACjD,UAAU,CAAC;wEACP,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK,KAAK,KAAK,IAAI,OAAO,EAAE,MAAM,CAAC,KAAK;wEAC/D,iBAAiB,OAAO,aAAa;oEACzC;oEACA,SAAS,CAAC,IAAM,EAAE,MAAM,CAAC,MAAM;oEAC/B,WAAU;oEACV,KAAI;oEACJ,MAAK;oEACL,WAAU;;;;;;8EAEd,6LAAC;oEAAI,WAAU;;sFACX,6LAAC;4EAAK,WAAU;4EAA6F,OAAO;gFAAE,OAAO,KAAK,KAAK,GAAG,IAAI,SAAS;4EAAO;;gFAAG;gFAC3J,KAAK,KAAK,CAAC,cAAc;;;;;;;sFAE/B,6LAAC;4EACG,MAAK;4EACL,SAAS,IAAM,eAAe;4EAC9B,WAAU;4EACV,cAAW;4EACX,OAAM;sFACT;;;;;;;;;;;;;2DAnDC;;;;;;;;;;;;;;;;sDA4DtB,6LAAC;4CAAI,WAAU;;8DACX,6LAAC;;sEACG,6LAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAGhE,6LAAC;4DACG,MAAK;4DACL,aAAY;4DACZ,OAAO,SAAS,UAAU,KAAK,IAAI,KAAK,SAAS,UAAU;4DAC3D,UAAU,CAAC;gEACP,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK,KAAK,KAAK,IAAI,OAAO,EAAE,MAAM,CAAC,KAAK;gEAC/D,YAAY,CAAA,OAAQ,CAAC;wEACjB,GAAG,IAAI;wEACP,YAAY;wEACZ,SAAS,KAAK,QAAQ,GAAG,MAAM,iCAAiC;oEACpE,CAAC;4DACL;4DACA,SAAS,CAAC,IAAM,EAAE,MAAM,CAAC,MAAM;4DAC/B,WAAU;4DACV,KAAI;4DACJ,MAAK;;;;;;;;;;;;8DAGb,6LAAC;oDAAI,WAAU;;sEACX,6LAAC;4DAAE,WAAU;;8EACT,6LAAC;oEAAK,WAAU;8EAAkC;;;;;;8EAClD,6LAAC;oEAAK,WAAU;;wEAAgB;wEAAE,SAAS,QAAQ,CAAC,cAAc;;;;;;;;;;;;;sEAEtE,6LAAC;4DAAE,WAAU;;8EACT,6LAAC;oEAAK,WAAU;8EAAkC;;;;;;8EAClD,6LAAC;oEAAK,WAAW,CAAC,cAAc,EAAE,SAAS,OAAO,GAAG,IAAI,iBAAiB,kBAAkB;;wEAAE;wEACxF,SAAS,OAAO,CAAC,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;sDAKjD,6LAAC;4CAAI,WAAU;;8DACX,6LAAC;oDACG,MAAK;oDACL,WAAU;8DACb;;;;;;8DAGD,6LAAC;oDACG,MAAK;oDACL,SAAS;oDACT,WAAU;8DACb;;;;;;;;;;;;;;;;;;;;;;;4BAOZ,6BACG,6LAAC;gCAAI,WAAU;gCACX,OAAO;oCACH,WAAW;oCACX,QAAQ;oCACR,oBAAoB;gCACxB;;kDACA,6LAAC;wCACG,KAAK;wCACL,WAAW,CAAC,iBAAiB,EAAE,gBAAgB,SAAS,cAAc,2BAA2B;wCACjG,OAAO,gBAAgB,SAAS;4CAC5B,gFAAgF;4CAChF,OAAO;4CACP,UAAU;4CACV,QAAQ;4CACR,SAAS;4CACT,WAAW;4CACX,iBAAiB;4CACjB,YAAY;4CACZ,UAAU;4CACV,YAAY;4CACZ,YAAY;4CACZ,QAAQ;4CACR,UAAU;4CACV,QAAQ;4CACR,OAAO;4CACP,iBAAiB;wCACrB,IAAI;4CACA,0BAA0B;4CAC1B,OAAO;4CACP,UAAU;4CACV,QAAQ;4CACR,SAAS;4CACT,WAAW;4CACX,iBAAiB;4CACjB,YAAY;4CACZ,UAAU;4CACV,YAAY;4CACZ,eAAe;4CACf,QAAQ;4CACR,UAAU;4CACV,QAAQ;4CACR,iBAAiB;4CACjB,OAAO;4CACP,iBAAiB;wCACrB;kDAEC,gBAAgB,SACb,6CAA6C;sDAC7C;;8DAEI,6LAAC;oDAAI,WAAU;;sEACX,6LAAC,gIAAA,CAAA,UAAK;4DACF,KAAI;4DACJ,KAAI;4DACJ,OAAO;4DACP,QAAQ;4DACR,WAAU;4DACV,OAAO;gEACH,UAAU;gEACV,QAAQ;gEACR,SAAS;gEACT,QAAQ;4DACZ;4DACA,UAAU;4DACV,aAAa;;;;;;sEAEjB,6LAAC;4DAAG,OAAO;gEACP,UAAU;gEACV,YAAY;gEACZ,OAAO;gEACP,cAAc;gEACd,WAAW;gEACX,eAAe;4DACnB;sEAAG;;;;;;sEAGH,6LAAC;4DAAI,OAAO;gEACR,UAAU;gEACV,YAAY;gEACZ,OAAO;gEACP,WAAW;gEACX,cAAc;gEACd,YAAY;4DAChB;;8EACI,6LAAC;8EAAI;;;;;;8EACL,6LAAC;8EAAI;;;;;;;;;;;;;;;;;;8DAKb,6LAAC;oDAAI,OAAO;wDACR,cAAc;wDACd,UAAU;wDACV,YAAY;wDACZ,OAAO;wDACP,WAAW;wDACX,cAAc;wDACd,SAAS,QAAQ,eAAe;oDACpC;;sEACI,6LAAC;4DAAI,OAAO;gEAAE,cAAc;gEAAO,YAAY;4DAAM;;8EACjD,6LAAC;8EAAO;;;;;;gEAAkB;gEAAE,SAAS,YAAY;;;;;;;sEAErD,6LAAC;4DAAI,OAAO;gEAAE,cAAc;gEAAO,YAAY;4DAAM;;8EACjD,6LAAC;8EAAO;;;;;;gEAAmB;gEAAE,SAAS,aAAa;;;;;;;sEAEvD,6LAAC;4DAAI,OAAO;gEAAE,cAAc;gEAAO,YAAY;4DAAM;;8EACjD,6LAAC;8EAAO;;;;;;gEAAa;gEAAE,SAAS,QAAQ;;;;;;;sEAE5C,6LAAC;4DAAI,OAAO;gEAAE,YAAY;4DAAM;;8EAC5B,6LAAC;8EAAO;;;;;;gEAAc;gEAAE,IAAI,KAAK,SAAS,IAAI,EAAE,kBAAkB,CAAC,SAAS;oEACxE,MAAM;oEACN,OAAO;oEACP,KAAK;gEACT;;;;;;;;;;;;;8DAKR,6LAAC;oDAAI,OAAO;wDAAE,cAAc;oDAAO;;sEAC/B,6LAAC;4DAAI,OAAO;gEACR,UAAU;gEACV,YAAY;gEACZ,WAAW;gEACX,cAAc;gEACd,OAAO;gEACP,eAAe;4DACnB;sEAAG;;;;;;wDAIF,SAAS,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBACvB,6LAAC;gEAAgB,OAAO;oEACpB,cAAc;oEACd,UAAU;oEACV,YAAY;oEACZ,OAAO;oEACP,cAAc,QAAQ,SAAS,KAAK,CAAC,MAAM,GAAG,IAAI,oBAAoB;oEACtE,eAAe;gEACnB;;kFACI,6LAAC;wEAAI,OAAO;4EACR,YAAY;4EACZ,cAAc;4EACd,UAAU,OAAO,oCAAoC;wEACzD;kFACK,KAAK,WAAW;;;;;;kFAErB,6LAAC;wEAAI,OAAO;4EACR,SAAS;4EACT,gBAAgB;4EAChB,UAAU;4EACV,YAAY;wEAChB;;0FACI,6LAAC;;oFAAM,KAAK,QAAQ;oFAAC;oFAAK,KAAK,SAAS,CAAC,cAAc;;;;;;;0FACvD,6LAAC;gFAAK,OAAO;oFAAE,YAAY;gFAAM;;oFAAG;oFAAE,KAAK,KAAK,CAAC,cAAc;;;;;;;;;;;;;;+DAtB7D;;;;;;;;;;;8DA6BlB,6LAAC;oDAAI,OAAO;wDACR,WAAW;wDACX,YAAY;wDACZ,UAAU;wDACV,YAAY;wDACZ,OAAO;oDACX;;sEACI,6LAAC;4DAAI,OAAO;gEACR,SAAS;gEACT,gBAAgB;gEAChB,cAAc;gEACd,YAAY;4DAChB;;8EACI,6LAAC;8EAAO;;;;;;8EACR,6LAAC;;wEAAO;wEAAE,SAAS,QAAQ,CAAC,cAAc;;;;;;;;;;;;;sEAE9C,6LAAC;4DAAI,OAAO;gEACR,SAAS;gEACT,gBAAgB;gEAChB,cAAc;gEACd,YAAY;4DAChB;;8EACI,6LAAC;8EAAO;;;;;;8EACR,6LAAC;;wEAAO;wEAAE,SAAS,UAAU,CAAC,cAAc;;;;;;;;;;;;;sEAEhD,6LAAC;4DAAI,OAAO;gEACR,SAAS;gEACT,gBAAgB;gEAChB,WAAW;gEACX,YAAY;gEACZ,WAAW;gEACX,UAAU;gEACV,YAAY;4DAChB;;8EACI,6LAAC;8EAAO;;;;;;8EACR,6LAAC;oEAAO,OAAO;wEACX,OAAO,SAAS,OAAO,GAAG,IAAI,YAAY;oEAC9C;;wEAAG;wEAAE,SAAS,OAAO,CAAC,cAAc;;;;;;;;;;;;;;;;;;;8DAK5C,6LAAC;oDAAI,OAAO;wDACR,WAAW;wDACX,YAAY;wDACZ,WAAW;wDACX,WAAW;wDACX,UAAU;wDACV,OAAO;oDACX;;sEACI,6LAAC;4DAAI,OAAO;gEACR,cAAc;gEACd,YAAY;gEACZ,UAAU,OAAO,+BAA+B;4DACpD;sEAAG;;;;;;sEAGH,6LAAC;4DAAI,OAAO;gEACR,cAAc;gEACd,YAAY;gEACZ,UAAU;4DACd;sEAAG;;;;;;sEAGH,6LAAC;4DAAI,OAAO;gEACR,UAAU;gEACV,OAAO;gEACP,YAAY;4DAChB;sEAAG;;;;;;;;;;;;;2DAMX,4CAA4C;sDAC5C;;8DACI,6LAAC;oDAAI,WAAU;;sEACX,6LAAC;4DAAI,WAAU;4DAAO,OAAO;gEAAE,QAAQ;gEAAQ,UAAU;4DAAW;sEAChE,cAAA,6LAAC,gIAAA,CAAA,UAAK;gEACF,KAAI;gEACJ,KAAI;gEACJ,OAAO;gEACP,QAAQ;gEACR,WAAU;gEACV,OAAO;oEACH,UAAU;oEACV,QAAQ;oEACR,SAAS;oEACT,QAAQ;gEACZ;gEACA,UAAU;gEACV,aAAa;;;;;;;;;;;sEAGrB,6LAAC;4DAAI,WAAU;;8EACX,6LAAC;oEAAG,OAAO;wEACP,UAAU;wEACV,YAAY;wEACZ,OAAO;wEACP,cAAc;oEAClB;8EAAG;;;;;;8EAGH,6LAAC;oEAAI,WAAU;oEACX,OAAO;wEACH,YAAY;wEACZ,QAAQ;oEACZ;;;;;;;;;;;;sEAKR,6LAAC;4DAAI,WAAU;4DAA6B,OAAO;gEAC/C,WAAW;gEACX,cAAc;gEACd,UAAU;gEACV,OAAO;4DACX;;8EAEI,6LAAC;oEAAI,WAAU;;sFACX,6LAAC;4EAAE,OAAO;gFACN,YAAY;gFACZ,cAAc;gFACd,UAAU;4EACd;sFAAG;;;;;;sFACH,6LAAC;4EAAI,WAAU;;8FACX,6LAAC;oFAAK,OAAO;wFAAE,SAAS;oFAAQ;8FAAG;;;;;;8FACnC,6LAAC;oFAAK,OAAO;wFAAE,SAAS;oFAAQ;8FAAG;;;;;;8FACnC,6LAAC;oFAAK,OAAO;wFAAE,SAAS;oFAAQ;8FAAG;;;;;;;;;;;;;;;;;;8EAK3C,6LAAC;oEAAI,WAAU;;sFACX,6LAAC;4EAAE,OAAO;gFACN,YAAY;gFACZ,cAAc;gFACd,UAAU;4EACd;sFAAG;;;;;;sFACH,6LAAC;4EAAI,WAAU;;8FACX,6LAAC;oFAAK,OAAO;wFAAE,SAAS;oFAAQ;8FAAG;;;;;;8FACnC,6LAAC;oFAAK,OAAO;wFAAE,SAAS;oFAAQ;8FAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8DAMnD,6LAAC;oDAAI,WAAU;oDAAO,OAAO;wDAAE,SAAS;wDAAQ,iBAAiB;wDAAW,cAAc;oDAAM;;sEAC5F,6LAAC;4DAAI,WAAU;4DAAiD,OAAO;gEAAE,OAAO;4DAAU;;8EACtF,6LAAC;oEAAE,WAAU;oEAAe,OAAO;wEAAE,OAAO;oEAAU;;sFAClD,6LAAC;4EAAO,OAAO;gFAAE,YAAY;4EAAM;sFAAG;;;;;;wEAAkB;wEAAE,SAAS,YAAY;;;;;;;8EAEnF,6LAAC;oEAAE,WAAU;oEAAe,OAAO;wEAAE,OAAO;oEAAU;;sFAClD,6LAAC;4EAAO,OAAO;gFAAE,YAAY;4EAAM;sFAAG;;;;;;wEAAmB;wEAAE,SAAS,aAAa;;;;;;;;;;;;;sEAGzF,6LAAC;4DAAI,WAAU;4DAA+C,OAAO;gEAAE,OAAO;4DAAU;;8EACpF,6LAAC;oEAAE,WAAU;oEAAe,OAAO;wEAAE,OAAO;oEAAU;;sFAClD,6LAAC;4EAAO,OAAO;gFAAE,YAAY;4EAAM;sFAAG;;;;;;wEAAa;wEAAE,SAAS,QAAQ;;;;;;;8EAE1E,6LAAC;oEAAE,OAAO;wEAAE,OAAO;oEAAU;;sFACzB,6LAAC;4EAAO,OAAO;gFAAE,YAAY;4EAAM;sFAAG;;;;;;wEAAc;wEAAE,IAAI,KAAK,SAAS,IAAI,EAAE,kBAAkB,CAAC,SAAS;4EACtG,MAAM;4EACN,OAAO;4EACP,KAAK;wEACT;;;;;;;;;;;;;;;;;;;8DAMZ,6LAAC;oDAAI,WAAU;8DACX,cAAA,6LAAC;wDAAG,OAAO;4DACP,YAAY;4DACZ,UAAU;4DACV,OAAO;4DACP,cAAc;wDAClB;kEAAG;;;;;;;;;;;8DAGP,6LAAC;oDAAI,WAAU;8DACX,cAAA,6LAAC;wDAAM,OAAO;4DACV,OAAO;4DACP,gBAAgB;4DAChB,cAAc;4DACd,OAAO;4DACP,UAAU;4DACV,UAAU;wDACd;;0EACI,6LAAC;0EACG,cAAA,6LAAC;oEAAG,OAAO;wEAAE,cAAc;wEAAqB,WAAW;oEAAoB;;sFAC3E,6LAAC;4EAAG,OAAO;gFACP,SAAS;gFACT,WAAW;gFACX,YAAY;gFACZ,OAAO;4EACX;sFAAG;;;;;;sFACH,6LAAC;4EAAG,OAAO;gFACP,SAAS;gFACT,WAAW;gFACX,YAAY;gFACZ,OAAO;4EACX;sFAAG;;;;;;sFACH,6LAAC;4EAAG,OAAO;gFACP,SAAS;gFACT,WAAW;gFACX,YAAY;gFACZ,OAAO;4EACX;sFAAG;;;;;;sFACH,6LAAC;4EAAG,OAAO;gFACP,SAAS;gFACT,WAAW;gFACX,YAAY;gFACZ,OAAO;4EACX;sFAAG;;;;;;;;;;;;;;;;;0EAGX,6LAAC;0EACI,SAAS,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBACvB,6LAAC;wEAAe,OAAO;4EAAE,cAAc;wEAAoB;;0FACvD,6LAAC;gFAAG,OAAO;oFAAE,SAAS;oFAAW,OAAO;gFAAU;0FAAI,KAAK,WAAW;;;;;;0FACtE,6LAAC;gFAAG,OAAO;oFAAE,SAAS;oFAAW,WAAW;oFAAS,OAAO;gFAAU;0FAAI,KAAK,QAAQ;;;;;;0FACvF,6LAAC;gFAAG,OAAO;oFAAE,SAAS;oFAAW,WAAW;oFAAS,OAAO;gFAAU;;oFAAG;oFAAE,KAAK,SAAS,CAAC,cAAc;;;;;;;0FACxG,6LAAC;gFAAG,OAAO;oFAAE,SAAS;oFAAW,WAAW;oFAAS,OAAO;gFAAU;;oFAAG;oFAAE,KAAK,KAAK,CAAC,cAAc;;;;;;;;uEAJ/F;;;;;;;;;;;;;;;;;;;;;8DAWzB,6LAAC;oDAAI,OAAO;wDACR,WAAW;wDACX,YAAY;wDACZ,OAAO;oDACX;8DACI,cAAA,6LAAC;wDAAI,WAAU;wDAA0B,OAAO;4DAAE,OAAO;wDAAU;;0EAC/D,6LAAC;gEAAI,WAAU;gEAAoC,OAAO;oEAAE,OAAO;gEAAU;;kFACzE,6LAAC;wEAAO,OAAO;4EACX,YAAY;4EACZ,UAAU;4EACV,OAAO;wEACX;kFAAG;;;;;;kFACH,6LAAC;wEAAK,OAAO;4EACT,YAAY;4EACZ,UAAU;4EACV,OAAO;wEACX;;4EAAG;4EAAE,SAAS,QAAQ,CAAC,cAAc;;;;;;;;;;;;;0EAGzC,6LAAC;gEAAI,WAAU;gEAAoC,OAAO;oEAAE,OAAO;gEAAU;;kFACzE,6LAAC;wEAAO,OAAO;4EACX,YAAY;4EACZ,UAAU;4EACV,OAAO;wEACX;kFAAG;;;;;;kFACH,6LAAC;wEAAK,OAAO;4EACT,YAAY;4EACZ,UAAU;4EACV,OAAO;wEACX;;4EAAG;4EAAE,SAAS,UAAU,CAAC,cAAc;;;;;;;;;;;;;0EAG3C,6LAAC;gEAAI,WAAU;gEAAuE,OAAO;oEAAE,OAAO;gEAAU;;kFAC5G,6LAAC;wEAAO,OAAO;4EACX,YAAY;4EACZ,UAAU;4EACV,OAAO;wEACX;kFAAG;;;;;;kFACH,6LAAC;wEAAK,OAAO;4EACT,YAAY;4EACZ,UAAU;4EACV,OAAO,SAAS,OAAO,GAAG,IAAI,YAAY;wEAC9C;;4EAAG;4EAAE,SAAS,OAAO,CAAC,cAAc;;;;;;;;;;;;;;;;;;;;;;;;8DAKhD,6LAAC;oDAAI,OAAO;wDACR,WAAW;wDACX,YAAY;wDACZ,WAAW;wDACX,WAAW;wDACX,OAAO;wDACP,UAAU;oDACd;;sEACI,6LAAC;4DAAE,OAAO;gEACN,cAAc;gEACd,YAAY;gEACZ,OAAO;4DACX;sEAAG;;;;;;sEACH,6LAAC;4DAAE,OAAO;gEACN,YAAY;gEACZ,OAAO;4DACX;sEAAG;;;;;;sEACH,6LAAC;4DAAI,OAAO;gEACR,WAAW;gEACX,UAAU;gEACV,OAAO;4DACX;sEAAG;;;;;;;;;;;;;;;;;;;kDAQnB,6LAAC;wCAAI,WAAU;kDACX,cAAA,6LAAC;4CAAI,WAAU;;8DACX,6LAAC;oDACG,SAAS;oDACT,WAAU;8DACb;;;;;;8DAGD,6LAAC;oDACG,SAAS;oDACT,WAAU;8DACb;;;;;;8DAGD,6LAAC;oDACG,SAAS;oDACT,WAAU;8DACb;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAUpB,+BACG,6LAAC;wBAAI,WAAU;kCACX,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACP,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAClC,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAE;4BAChC,MAAM;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAC/B,WAAU;;8CAEV,6LAAC;oCAAI,WAAU;;sDACX,6LAAC;4CAAG,WAAU;sDAAuC;;;;;;sDAGrD,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAKzC,6LAAC;oCAAI,WAAU;;sDAEX,6LAAC;4CACG,SAAS,IAAM,oBAAoB;4CACnC,WAAU;sDAEV,cAAA,6LAAC;gDAAI,WAAU;;kEACX,6LAAC;;0EACG,6LAAC;gEAAG,WAAU;0EAAwD;;;;;;0EAGtE,6LAAC;gEAAE,WAAU;0EAA6B;;;;;;;;;;;;kEAI9C,6LAAC;wDAAI,WAAU;kEAAsD;;;;;;;;;;;;;;;;;sDAO7E,6LAAC;4CACG,SAAS,IAAM,oBAAoB;4CACnC,WAAU;sDAEV,cAAA,6LAAC;gDAAI,WAAU;;kEACX,6LAAC;;0EACG,6LAAC;gEAAG,WAAU;0EAAyD;;;;;;0EAGvE,6LAAC;gEAAE,WAAU;0EAA6B;;;;;;;;;;;;kEAI9C,6LAAC;wDAAI,WAAU;kEAAsD;;;;;;;;;;;;;;;;;;;;;;;8CAOjF,6LAAC;oCAAI,WAAU;8CACX,cAAA,6LAAC;wCACG,SAAS,IAAM,iBAAiB;wCAChC,WAAU;kDACb;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjC;GA93DM;KAAA;uCAg4DS"}}, {"offset": {"line": 3168, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}