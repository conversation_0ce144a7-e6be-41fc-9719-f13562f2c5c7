{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/Code/Web%20Projects/Client%20Projects/Cepoka%20Website/cepoka/src/app/Components/SpinningLoader.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\n\ninterface SpinningLoaderProps {\n  size?: 'small' | 'medium' | 'large';\n  className?: string;\n  text?: string;\n}\n\nconst SpinningLoader: React.FC<SpinningLoaderProps> = ({ \n  size = 'medium', \n  className = '',\n  text\n}) => {\n  // Size mapping\n  const sizeMap = {\n    small: 'w-6 h-6 border-2',\n    medium: 'w-10 h-10 border-3',\n    large: 'w-16 h-16 border-4',\n  };\n\n  const sizeClass = sizeMap[size];\n  \n  return (\n    <div className={`flex flex-col items-center justify-center ${className}`}>\n      <motion.div\n        className={`${sizeClass} rounded-full border-t-pink-500 border-r-blue-500 border-b-pink-500 border-l-blue-500`}\n        animate={{ rotate: 360 }}\n        transition={{\n          duration: 1.5,\n          repeat: Infinity,\n          ease: \"linear\"\n        }}\n        style={{ borderStyle: 'solid' }}\n      />\n      {text && (\n        <p className=\"mt-3 text-sm text-gray-600 font-medium\">{text}</p>\n      )}\n    </div>\n  );\n};\n\nexport default SpinningLoader;\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAWA,MAAM,iBAAgD,CAAC,EACrD,OAAO,QAAQ,EACf,YAAY,EAAE,EACd,IAAI,EACL;IACC,eAAe;IACf,MAAM,UAAU;QACd,OAAO;QACP,QAAQ;QACR,OAAO;IACT;IAEA,MAAM,YAAY,OAAO,CAAC,KAAK;IAE/B,qBACE,8OAAC;QAAI,WAAW,CAAC,0CAA0C,EAAE,WAAW;;0BACtE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAW,GAAG,UAAU,qFAAqF,CAAC;gBAC9G,SAAS;oBAAE,QAAQ;gBAAI;gBACvB,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,MAAM;gBACR;gBACA,OAAO;oBAAE,aAAa;gBAAQ;;;;;;YAE/B,sBACC,8OAAC;gBAAE,WAAU;0BAA0C;;;;;;;;;;;;AAI/D;uCAEe"}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/Code/Web%20Projects/Client%20Projects/Cepoka%20Website/cepoka/src/lib/appwrite.ts"], "sourcesContent": ["import { Client, Account, Databases, Storage } from \"appwrite\";\r\n\r\n// Initialize the Appwrite client\r\nconst client = new Client()\r\n  .setEndpoint(\"https://cloud.appwrite.io/v1\")\r\n  .setProject(\"67d07dc9000bafdd5d81\"); // Confirmed correct project ID\r\n\r\nexport const account = new Account(client);\r\nexport const databases = new Databases(client);\r\nexport const storage = new Storage(client);\r\n\r\nexport const appwriteConfig = {\r\n  // Using the confirmed database ID\r\n  databaseId: \"6813eadb003e7d64f63c\",\r\n  productsCollectionId: \"6813eaf40036e52c29b1\",\r\n  categoriesCollectionId: \"6817640f000dd0b67c77\",\r\n  stockProductsCollectionId: \"681a651d001cc3de8395\",\r\n  stockMovementsCollectionId: \"681bddcc000204a3748d\",\r\n  storageId: \"6813ea36001624c1202a\",\r\n};\r\n\r\n// project id: 67d07d7b0010f39ec77d\r\n// database id: 67d8833d000778157021\r\n// collection id: 67d8835b002502c5d7ba\r\n// storage id: 67d8841a001213adf116\r\n"], "names": [], "mappings": ";;;;;;AAAA;;AAEA,iCAAiC;AACjC,MAAM,SAAS,IAAI,8IAAA,CAAA,SAAM,GACtB,WAAW,CAAC,gCACZ,UAAU,CAAC,yBAAyB,+BAA+B;AAE/D,MAAM,UAAU,IAAI,8IAAA,CAAA,UAAO,CAAC;AAC5B,MAAM,YAAY,IAAI,8IAAA,CAAA,YAAS,CAAC;AAChC,MAAM,UAAU,IAAI,8IAAA,CAAA,UAAO,CAAC;AAE5B,MAAM,iBAAiB;IAC5B,kCAAkC;IAClC,YAAY;IACZ,sBAAsB;IACtB,wBAAwB;IACxB,2BAA2B;IAC3B,4BAA4B;IAC5B,WAAW;AACb,GAEA,mCAAmC;CACnC,oCAAoC;CACpC,sCAAsC;CACtC,mCAAmC"}}, {"offset": {"line": 91, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 97, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/Code/Web%20Projects/Client%20Projects/Cepoka%20Website/cepoka/src/app/admin/stock-manager/%5Bid%5D/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useRef, useEffect } from 'react';\r\nimport { useRouter, useParams } from 'next/navigation';\r\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\nimport { motion } from 'framer-motion';\r\nimport toast from 'react-hot-toast';\r\nimport SpinningLoader from '@/src/app/Components/SpinningLoader';\r\nimport Script from 'next/script';\r\nimport { format, parseISO } from 'date-fns';\r\nimport { databases, appwriteConfig } from '@/src/lib/appwrite';\r\n\r\n// Interface for stock movement data\r\ninterface StockMovement {\r\n  date: string;\r\n  stockedIn: number;\r\n  stockedOut: number;\r\n  remarks: string;\r\n  totalStock: number;\r\n  balance: number;\r\n  sign?: string;\r\n  id?: string; // Unique identifier for each movement\r\n}\r\n\r\n// Interface for stock product data\r\ninterface StockProduct {\r\n  $id: string;\r\n  name: string;\r\n  stockMovements: StockMovement[];\r\n  lastUpdated: string;\r\n  $createdAt: string;\r\n}\r\n\r\n// Interface for form data\r\ninterface StockMovementFormData {\r\n  date: string;\r\n  stockedIn: number;\r\n  stockedOut: number;\r\n  remarks: string;\r\n  sign: string;\r\n}\r\n\r\n// We'll use a different approach to get the ID from the URL\r\n// Define the component as a client component that gets params from useParams\r\nexport default function StockProductPage() {\r\n  // Get the ID from the URL using useParams\r\n  const params = useParams();\r\n  const productId = params?.id as string;\r\n\r\n  const router = useRouter();\r\n  const [stockProduct, setStockProduct] = useState<StockProduct | null>(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n  const [isDeleting, setIsDeleting] = useState(false);\r\n  const [showDeleteModal, setShowDeleteModal] = useState(false);\r\n  const [showAddMovementForm, setShowAddMovementForm] = useState(false);\r\n  const [editingMovementId, setEditingMovementId] = useState<string | null>(null);\r\n  const [isUpdating, setIsUpdating] = useState(false);\r\n  const [newMovement, setNewMovement] = useState<StockMovementFormData>({\r\n    date: new Date().toISOString().split('T')[0],\r\n    stockedIn: 0,\r\n    stockedOut: 0,\r\n    remarks: '',\r\n    sign: '',\r\n  });\r\n  const [editedMovement, setEditedMovement] = useState<StockMovement | null>(null);\r\n  const stockCardRef = useRef<HTMLDivElement>(null);\r\n\r\n  // Fetch stock product data\r\n  useEffect(() => {\r\n    const fetchStockProduct = async () => {\r\n      try {\r\n        setLoading(true);\r\n\r\n        try {\r\n          const response = await databases.getDocument(\r\n            appwriteConfig.databaseId,\r\n            appwriteConfig.stockProductsCollectionId,\r\n            productId\r\n          );\r\n\r\n          const stockProductData = response as unknown as Record<string, unknown>;\r\n\r\n          // Ensure stockMovements is an array\r\n          if (!stockProductData.stockMovements) {\r\n            stockProductData.stockMovements = [];\r\n          }\r\n\r\n          // Parse each stock movement from string to object\r\n          const stockMovementsArray = Array.isArray(stockProductData.stockMovements)\r\n            ? stockProductData.stockMovements as (string | Record<string, unknown>)[]\r\n            : [];\r\n\r\n          const parsedStockMovements = stockMovementsArray.map((movement, index: number) => {\r\n            if (typeof movement === 'string') {\r\n              try {\r\n                const parsedMovement = JSON.parse(movement);\r\n                // Add a unique ID to each movement for editing purposes\r\n                return { ...parsedMovement, id: `movement-${index}` };\r\n              } catch (error) {\r\n                console.error('Error parsing stock movement:', error);\r\n                return null;\r\n              }\r\n            }\r\n            // Add ID to existing object movements too\r\n            return { ...movement, id: `movement-${index}` };\r\n          }).filter(Boolean); // Remove any null values\r\n\r\n          // Create a properly formatted StockProduct object\r\n          const formattedStockProduct: StockProduct = {\r\n            $id: (stockProductData.$id as string) || productId,\r\n            name: (stockProductData.name as string) || 'Unknown Product',\r\n            $createdAt: (stockProductData.$createdAt as string) || new Date().toISOString(),\r\n            lastUpdated: (stockProductData.lastUpdated as string) || new Date().toISOString(),\r\n            stockMovements: parsedStockMovements\r\n          };\r\n\r\n          setStockProduct(formattedStockProduct);\r\n        } catch (error) {\r\n          console.error(\"Error fetching stock product:\", error);\r\n\r\n          // Fallback to dummy data if there's an error\r\n          const dummyStockMovements: StockMovement[] = [\r\n            {\r\n              date: \"2023-05-01T12:00:00.000Z\",\r\n              stockedIn: 10,\r\n              stockedOut: 0,\r\n              remarks: \"Initial stock\",\r\n              totalStock: 10,\r\n              balance: 10,\r\n              sign: \"John Doe\"\r\n            },\r\n            {\r\n              date: \"2023-05-10T14:30:00.000Z\",\r\n              stockedIn: 5,\r\n              stockedOut: 0,\r\n              remarks: \"Restocked\",\r\n              totalStock: 15,\r\n              balance: 15,\r\n              sign: \"Jane Smith\"\r\n            },\r\n            {\r\n              date: \"2023-05-15T09:45:00.000Z\",\r\n              stockedIn: 0,\r\n              stockedOut: 3,\r\n              remarks: \"Sold to customer\",\r\n              totalStock: 15,\r\n              balance: 12,\r\n              sign: \"John Doe\"\r\n            }\r\n          ];\r\n\r\n          const dummyProduct: StockProduct = {\r\n            $id: productId,\r\n            name: productId === \"1\" ? \"Salon Chair\" : productId === \"2\" ? \"Hair Dryer\" : \"Facial Steamer\",\r\n            stockMovements: dummyStockMovements,\r\n            lastUpdated: \"2023-05-15T09:45:00.000Z\",\r\n            $createdAt: \"2023-05-01T12:00:00.000Z\"\r\n          };\r\n\r\n          setStockProduct(dummyProduct);\r\n          throw error; // Re-throw to be caught by the outer try-catch\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error fetching stock product:\", error);\r\n        toast.error(\"Failed to load stock product\");\r\n        router.push('/admin/stock-manager');\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchStockProduct();\r\n  }, [productId, router]);\r\n\r\n  // Handle form input changes for new movement\r\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\r\n    const { name, value } = e.target;\r\n    setNewMovement(prev => ({\r\n      ...prev,\r\n      [name]: name === 'stockedIn' || name === 'stockedOut' ? Number(value) : value\r\n    }));\r\n  };\r\n\r\n  // Handle form input changes for edited movement\r\n  const handleEditInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\r\n    if (!editedMovement) return;\r\n\r\n    const { name, value } = e.target;\r\n\r\n    setEditedMovement(prev => {\r\n      if (!prev) return prev;\r\n      return {\r\n        ...prev,\r\n        [name]: name === 'stockedIn' || name === 'stockedOut' ? Number(value) : value\r\n      };\r\n    });\r\n  };\r\n\r\n  // Start editing a movement\r\n  const startEditingMovement = (movement: StockMovement) => {\r\n    setEditingMovementId(movement.id || null);\r\n    setEditedMovement({ ...movement });\r\n    setShowAddMovementForm(false); // Close add form if open\r\n  };\r\n\r\n  // Cancel editing\r\n  const cancelEditing = () => {\r\n    setEditingMovementId(null);\r\n    setEditedMovement(null);\r\n  };\r\n\r\n  // Save edited movement\r\n  const saveEditedMovement = async () => {\r\n    if (!stockProduct || !editedMovement || !editingMovementId) return;\r\n\r\n    try {\r\n      setIsUpdating(true);\r\n\r\n      // Find the index of the movement being edited\r\n      const movementIndex = stockProduct.stockMovements.findIndex(m => m.id === editingMovementId);\r\n      if (movementIndex === -1) {\r\n        throw new Error('Movement not found');\r\n      }\r\n\r\n      // Create updated movements array\r\n      const updatedMovements = [...stockProduct.stockMovements];\r\n      updatedMovements[movementIndex] = editedMovement;\r\n\r\n      // Recalculate totals and balances for all movements after the edited one\r\n      for (let i = movementIndex; i < updatedMovements.length; i++) {\r\n        if (i === 0) {\r\n          // First movement\r\n          updatedMovements[i].totalStock = updatedMovements[i].stockedIn;\r\n          updatedMovements[i].balance = updatedMovements[i].totalStock - updatedMovements[i].stockedOut;\r\n        } else {\r\n          // Subsequent movements\r\n          const prevMovement = updatedMovements[i - 1];\r\n          updatedMovements[i].totalStock = prevMovement.balance + updatedMovements[i].stockedIn;\r\n          updatedMovements[i].balance = updatedMovements[i].totalStock - updatedMovements[i].stockedOut;\r\n        }\r\n      }\r\n\r\n      // Convert movements to strings for Appwrite\r\n      const updatedMovementStrings = updatedMovements.map(movement => {\r\n        // Create a copy without the id field which is only used for UI\r\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n        const { id, ...movementWithoutId } = movement;\r\n        return JSON.stringify(movementWithoutId);\r\n      });\r\n\r\n      // Update in Appwrite\r\n      await databases.updateDocument(\r\n        appwriteConfig.databaseId,\r\n        appwriteConfig.stockProductsCollectionId,\r\n        productId,\r\n        {\r\n          stockMovements: updatedMovementStrings,\r\n          lastUpdated: new Date().toISOString()\r\n        }\r\n      );\r\n\r\n      // Update local state\r\n      const updatedStockProduct = {\r\n        ...stockProduct,\r\n        stockMovements: updatedMovements,\r\n        lastUpdated: new Date().toISOString()\r\n      };\r\n\r\n      setStockProduct(updatedStockProduct);\r\n      setEditingMovementId(null);\r\n      setEditedMovement(null);\r\n      toast.success('Stock movement updated successfully');\r\n    } catch (error) {\r\n      console.error('Error updating stock movement:', error);\r\n      toast.error('Failed to update stock movement');\r\n    } finally {\r\n      setIsUpdating(false);\r\n    }\r\n  };\r\n\r\n  // Handle adding new stock movement\r\n  const handleAddMovement = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n\r\n    if (!stockProduct) return;\r\n\r\n    try {\r\n      setIsSubmitting(true);\r\n\r\n      // Calculate new totals\r\n      const lastMovement = stockProduct.stockMovements[stockProduct.stockMovements.length - 1];\r\n      const newTotalStock = lastMovement.totalStock + newMovement.stockedIn;\r\n      const newBalance = newTotalStock - newMovement.stockedOut;\r\n\r\n      // Create new stock movement\r\n      const stockMovement: StockMovement = {\r\n        date: new Date(newMovement.date).toISOString(),\r\n        stockedIn: newMovement.stockedIn,\r\n        stockedOut: newMovement.stockedOut,\r\n        remarks: newMovement.remarks,\r\n        totalStock: newTotalStock,\r\n        balance: newBalance,\r\n        sign: newMovement.sign\r\n      };\r\n\r\n      // Add to existing stock movements\r\n      const updatedStockMovements = [...stockProduct.stockMovements, stockMovement];\r\n\r\n      // Convert the new movement to a string\r\n      const stockMovementString = JSON.stringify(stockMovement);\r\n\r\n      // Get the current stockMovements array from Appwrite (as strings)\r\n      let currentStockMovementsStrings: string[] = [];\r\n      try {\r\n        const currentDoc = await databases.getDocument(\r\n          appwriteConfig.databaseId,\r\n          appwriteConfig.stockProductsCollectionId,\r\n          productId\r\n        );\r\n\r\n        currentStockMovementsStrings = currentDoc.stockMovements || [];\r\n      } catch (error) {\r\n        console.error('Error fetching current stock movements:', error);\r\n        currentStockMovementsStrings = [];\r\n      }\r\n\r\n      // Add the new movement string to the array\r\n      const updatedStockMovementsStrings = [...currentStockMovementsStrings, stockMovementString];\r\n\r\n      // Update stock product\r\n      const updatedStockProduct = {\r\n        ...stockProduct,\r\n        stockMovements: updatedStockMovements,\r\n        lastUpdated: new Date().toISOString()\r\n      };\r\n\r\n      try {\r\n        await databases.updateDocument(\r\n          appwriteConfig.databaseId,\r\n          appwriteConfig.stockProductsCollectionId,\r\n          productId,\r\n          {\r\n            stockMovements: updatedStockMovementsStrings, // Send array of strings\r\n            lastUpdated: new Date().toISOString()\r\n          }\r\n        );\r\n      } catch (error) {\r\n        console.error('Error updating stock product in Appwrite:', error);\r\n        throw error; // Re-throw to be caught by the outer try-catch\r\n      }\r\n\r\n      // Update local state\r\n      setStockProduct(updatedStockProduct);\r\n\r\n      // Reset form\r\n      setNewMovement({\r\n        date: new Date().toISOString().split('T')[0],\r\n        stockedIn: 0,\r\n        stockedOut: 0,\r\n        remarks: '',\r\n        sign: '',\r\n      });\r\n\r\n      setShowAddMovementForm(false);\r\n      toast.success('Stock movement added successfully');\r\n    } catch (error) {\r\n      console.error('Error adding stock movement:', error);\r\n      toast.error('Failed to add stock movement');\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  // Handle deleting stock product\r\n  const handleDelete = async () => {\r\n    try {\r\n      setIsDeleting(true);\r\n\r\n      try {\r\n        await databases.deleteDocument(\r\n          appwriteConfig.databaseId,\r\n          appwriteConfig.stockProductsCollectionId,\r\n          productId\r\n        );\r\n      } catch (error) {\r\n        console.error('Error deleting stock product from Appwrite:', error);\r\n        throw error; // Re-throw to be caught by the outer try-catch\r\n      }\r\n\r\n      toast.success('Stock product deleted successfully');\r\n\r\n      // Navigate back to stock manager page\r\n      router.push('/admin/stock-manager');\r\n    } catch (error) {\r\n      console.error('Error deleting stock product:', error);\r\n      toast.error('Failed to delete stock product');\r\n      setIsDeleting(false);\r\n      setShowDeleteModal(false);\r\n    }\r\n  };\r\n\r\n  // Generate and download PDF\r\n  const downloadPDF = async () => {\r\n    try {\r\n      if (stockCardRef.current) {\r\n        // Show loading toast\r\n        toast.loading(\r\n          <div className=\"flex items-center gap-2\">\r\n            <SpinningLoader size=\"small\" />\r\n            <span>Generating PDF...</span>\r\n          </div>\r\n        );\r\n\r\n        // Use a timeout to ensure the UI updates before PDF generation\r\n        await new Promise(resolve => setTimeout(resolve, 100));\r\n\r\n        const element = stockCardRef.current;\r\n\r\n        // Wait for fonts and images to load\r\n        await document.fonts.ready;\r\n\r\n        // Wait for all images to load\r\n        const images = Array.from(element.getElementsByTagName('img'));\r\n        await Promise.all(images.map(img => {\r\n          if (img.complete) return Promise.resolve();\r\n          return new Promise(resolve => {\r\n            img.onload = resolve;\r\n            img.onerror = resolve;\r\n          });\r\n        }));\r\n\r\n        // Force a delay to ensure all content is rendered and visible\r\n        await new Promise(resolve => setTimeout(resolve, 1000));\r\n\r\n        // Temporarily adjust styles for PDF generation\r\n        const originalOverflow = element.style.overflow;\r\n        const originalMaxWidth = element.style.maxWidth;\r\n        const tableElement = element.querySelector('table');\r\n        const originalTableWidth = tableElement?.style.width;\r\n\r\n        // Apply PDF-friendly styles temporarily\r\n        element.style.overflow = 'visible';\r\n        element.style.maxWidth = 'none';\r\n        if (tableElement) {\r\n          tableElement.style.width = '100%';\r\n          tableElement.style.fontSize = '11px';\r\n        }\r\n\r\n        // Generate PDF with filename\r\n        const filename = `Stock_Card_${stockProduct?.name.replace(/\\s+/g, '_')}.pdf`;\r\n\r\n        // Use unknown type and then cast to appropriate type\r\n        // This avoids complex type definition issues with the html2pdf library\r\n        interface Html2PdfReturn {\r\n          set: (options: Record<string, unknown>) => Html2PdfReturn;\r\n          from: (element: HTMLElement) => Html2PdfReturn;\r\n          save: () => Promise<void>;\r\n        }\r\n\r\n        // Use a more specific type without any\r\n        const html2pdf = window.html2pdf as unknown as () => Html2PdfReturn;\r\n        const pdfInstance = html2pdf();\r\n\r\n        // Configure and generate PDF with optimized settings for full content capture\r\n        pdfInstance.set({\r\n          margin: [8, 8, 8, 8], // Smaller margins for more content space\r\n          filename: filename,\r\n          image: { type: 'jpeg', quality: 0.95 },\r\n          html2canvas: {\r\n            scale: 1.5, // Reduced scale to ensure full content fits\r\n            useCORS: true,\r\n            logging: true, // Enable logging for debugging\r\n            letterRendering: true,\r\n            allowTaint: true,\r\n            backgroundColor: '#ffffff',\r\n            windowWidth: 1400, // Increased width for wide table\r\n            windowHeight: 2000, // Increased height for full content\r\n            scrollX: 0,\r\n            scrollY: 0,\r\n            foreignObjectRendering: false, // Disable for better compatibility\r\n            onrendered: function (canvas) {\r\n              console.log('Canvas rendered:', canvas.width, 'x', canvas.height);\r\n            }\r\n          },\r\n          jsPDF: {\r\n            unit: 'mm',\r\n            format: 'a3', // Use A3 for more space (297mm x 420mm)\r\n            orientation: 'landscape', // Landscape for wide table\r\n            compress: true,\r\n            precision: 3,\r\n            hotfixes: [\"px_scaling\"] // Fix for scaling issues\r\n          }\r\n        });\r\n\r\n        await pdfInstance.from(element).save();\r\n\r\n        // Restore original styles\r\n        element.style.overflow = originalOverflow;\r\n        element.style.maxWidth = originalMaxWidth;\r\n        if (tableElement && originalTableWidth) {\r\n          tableElement.style.width = originalTableWidth;\r\n        }\r\n\r\n        toast.dismiss();\r\n        toast.success('PDF downloaded successfully');\r\n      } else {\r\n        toast.error('PDF generation not available');\r\n      }\r\n    } catch (error) {\r\n      console.error('Error generating PDF:', error);\r\n      toast.dismiss();\r\n      toast.error('Failed to generate PDF');\r\n    }\r\n  };\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"min-h-screen flex items-center justify-center\">\r\n        <SpinningLoader size=\"large\" text=\"Loading stock product...\" />\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (!stockProduct) {\r\n    return (\r\n      <div className=\"p-4 max-w-7xl mt-28 sm:mt-32 md:mt-40 mx-auto pt-8 sm:pt-10\">\r\n        <div className=\"bg-white p-6 rounded-lg shadow-sm border text-center\">\r\n          <h2 className=\"text-xl font-semibold text-gray-900 mb-2\">Stock Product Not Found</h2>\r\n          <p className=\"text-gray-700 mb-4\">The stock product you&apos;re looking for doesn&apos;t exist or has been deleted.</p>\r\n          <Link\r\n            href=\"/admin/stock-manager\"\r\n            className=\"inline-flex items-center text-blue-600 hover:text-blue-800 px-4 py-2 rounded-lg active:bg-blue-50 transition-all duration-200 touch-manipulation\"\r\n            style={{ WebkitTapHighlightColor: 'transparent' }}\r\n          >\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              className=\"h-5 w-5 mr-1\"\r\n              fill=\"none\"\r\n              viewBox=\"0 0 24 24\"\r\n              stroke=\"currentColor\"\r\n            >\r\n              <path\r\n                strokeLinecap=\"round\"\r\n                strokeLinejoin=\"round\"\r\n                strokeWidth={2}\r\n                d=\"M10 19l-7-7m0 0l7-7m-7 7h18\"\r\n              />\r\n            </svg>\r\n            Back to Stock Manager\r\n          </Link>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"p-4 max-w-7xl mt-28 sm:mt-32 md:mt-40 mx-auto pt-8 sm:pt-10\">\r\n      <Script src=\"https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js\" />\r\n\r\n      {/* Back button with animation - improved for mobile */}\r\n      <div className=\"mb-6\">\r\n        <Link\r\n          href=\"/admin/stock-manager\"\r\n          className=\"inline-flex items-center px-4 py-3 rounded-lg text-gray-700 hover:text-black hover:bg-gray-100 active:bg-gray-200 transition-all duration-200 touch-manipulation\"\r\n          style={{ WebkitTapHighlightColor: 'transparent' }}\r\n        >\r\n          <svg\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            className=\"h-5 w-5 mr-2\"\r\n            fill=\"none\"\r\n            viewBox=\"0 0 24 24\"\r\n            stroke=\"currentColor\"\r\n          >\r\n            <path\r\n              strokeLinecap=\"round\"\r\n              strokeLinejoin=\"round\"\r\n              strokeWidth={2}\r\n              d=\"M10 19l-7-7m0 0l7-7m-7 7h18\"\r\n            />\r\n          </svg>\r\n          Back to Stock Manager\r\n        </Link>\r\n      </div>\r\n\r\n      {/* Header */}\r\n      <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6\">\r\n        <div>\r\n          <h1 className=\"text-4xl font-bold text-gray-900 flex items-center\">\r\n            <span className=\"text-gray-900 mr-2 inline-block\">📋</span>\r\n            {stockProduct.name}\r\n          </h1>\r\n          <p className=\"text-gray-700 mt-1 text-lg\">\r\n            Created: <span className=\"font-medium\">{format(parseISO(stockProduct.$createdAt), \"MMM dd, yyyy\")}</span> | Last Updated: <span className=\"font-medium\">{format(parseISO(stockProduct.lastUpdated), \"MMM dd, yyyy\")}</span>\r\n          </p>\r\n        </div>\r\n\r\n        <div className=\"flex gap-2 mt-4 sm:mt-0\">\r\n          <motion.button\r\n            whileHover={{ scale: 1.02 }}\r\n            whileTap={{ scale: 0.98 }}\r\n            onClick={downloadPDF}\r\n            className=\"bg-green-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-green-700 transition-all\"\r\n          >\r\n            Download PDF\r\n          </motion.button>\r\n\r\n          <motion.button\r\n            whileHover={{ scale: 1.02 }}\r\n            whileTap={{ scale: 0.98 }}\r\n            onClick={() => setShowDeleteModal(true)}\r\n            className=\"bg-red-50 text-red-600 px-4 py-2 rounded-lg font-medium hover:bg-red-100 transition-all\"\r\n          >\r\n            Delete\r\n          </motion.button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Stock Card */}\r\n      <div className=\"bg-gray-50 p-6 rounded-lg shadow-sm border border-gray-200 mb-6\">\r\n        <div ref={stockCardRef} className=\"p-4\">\r\n          <div className=\"mb-6\">\r\n            <div className=\"flex items-center justify-center mb-2\">\r\n              <div className=\"flex items-center\">\r\n                <Image\r\n                  src=\"/logo.png\"\r\n                  alt=\"Cepoka Logo\"\r\n                  width={64}\r\n                  height={64}\r\n                  className=\"mr-5 drop-shadow-md\"\r\n                  style={{ objectFit: \"contain\" }}\r\n                />\r\n                <div>\r\n                  <h2 className=\"text-3xl font-bold uppercase text-gray-900 drop-shadow-sm\">CEPOKA BEAUTY HUB</h2>\r\n                  <h3 className=\"text-xl font-semibold text-gray-900\">STOCK CARD</h3>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className=\"border-t border-b border-gray-300 py-3 my-4 relative overflow-hidden\">\r\n              <div className=\"absolute inset-0 flex items-center justify-center opacity-10 blur-md print-watermark\">\r\n                <Image\r\n                  src=\"/logo.png\"\r\n                  alt=\"Cepoka Logo\"\r\n                  width={192}\r\n                  height={192}\r\n                  className=\"object-contain\"\r\n                  priority={false}\r\n                />\r\n              </div>\r\n              <div className=\"flex justify-between items-center px-4 relative z-10\">\r\n                <div className=\"text-gray-900 font-bold text-xl md:text-2xl\">\r\n                  Product:\r\n                </div>\r\n                <div className=\"text-gray-900 font-extrabold text-xl md:text-2xl\">\r\n                  {stockProduct.name}\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Add print-specific styles */}\r\n            <style jsx global>{`\r\n              /* PDF Generation Styles */\r\n              .print-watermark {\r\n                position: absolute !important;\r\n                top: 50% !important;\r\n                left: 50% !important;\r\n                transform: translate(-50%, -50%) !important;\r\n                opacity: 0.1 !important;\r\n                z-index: 1 !important;\r\n              }\r\n\r\n              /* Ensure table is fully visible in PDF */\r\n              table {\r\n                width: 100% !important;\r\n                table-layout: fixed !important;\r\n                border-collapse: collapse !important;\r\n                font-size: 11px !important;\r\n                line-height: 1.3 !important;\r\n              }\r\n\r\n              table th, table td {\r\n                color: #111827 !important;\r\n                border: 1px solid #d1d5db !important;\r\n                padding: 6px 4px !important;\r\n                font-size: 11px !important;\r\n                word-wrap: break-word !important;\r\n                overflow-wrap: break-word !important;\r\n                vertical-align: top !important;\r\n              }\r\n\r\n              table th {\r\n                background-color: #f3f4f6 !important;\r\n                font-weight: 600 !important;\r\n              }\r\n\r\n              h2, h3 {\r\n                color: #111827 !important;\r\n                font-weight: bold !important;\r\n              }\r\n\r\n              img {\r\n                max-width: 100% !important;\r\n                height: auto !important;\r\n                object-fit: contain !important;\r\n              }\r\n\r\n              /* Ensure content doesn't get cut off */\r\n              .overflow-x-auto {\r\n                overflow: visible !important;\r\n              }\r\n\r\n              /* PDF specific adjustments */\r\n              @media print {\r\n                @page {\r\n                  size: A3 landscape;\r\n                  margin: 8mm;\r\n                }\r\n\r\n                body {\r\n                  -webkit-print-color-adjust: exact !important;\r\n                  print-color-adjust: exact !important;\r\n                }\r\n              }\r\n            `}</style>\r\n          </div>\r\n\r\n          <div className=\"overflow-x-auto\">\r\n            <table className=\"min-w-full border-collapse border border-gray-300 shadow-md\">\r\n              <thead>\r\n                <tr className=\"bg-gray-100\">\r\n                  <th className=\"border border-gray-300 px-4 py-2 text-gray-900 font-semibold\">Date</th>\r\n                  <th className=\"border border-gray-300 px-4 py-2 text-gray-900 font-semibold\">Qty</th>\r\n                  <th className=\"border border-gray-300 px-4 py-2 text-gray-900 font-semibold\">Stocked In</th>\r\n                  <th className=\"border border-gray-300 px-4 py-2 text-gray-900 font-semibold\">Total Stock</th>\r\n                  <th className=\"border border-gray-300 px-4 py-2 text-gray-900 font-semibold\">Stocked Out</th>\r\n                  <th className=\"border border-gray-300 px-4 py-2 text-gray-900 font-semibold\">Balance</th>\r\n                  <th className=\"border border-gray-300 px-4 py-2 text-gray-900 font-semibold\">Remarks</th>\r\n                  <th className=\"border border-gray-300 px-4 py-2 text-gray-900 font-semibold\">Sign</th>\r\n                  <th className=\"border border-gray-300 px-4 py-2 text-gray-900 font-semibold\">Actions</th>\r\n                </tr>\r\n              </thead>\r\n              <tbody>\r\n                {stockProduct.stockMovements.map((movement, index) => (\r\n                  editingMovementId === movement.id ? (\r\n                    // Edit form row\r\n                    <tr\r\n                      key={`edit-${movement.id}`}\r\n                      className=\"bg-blue-50\"\r\n                      onClick={(e) => e.stopPropagation()}\r\n                    >\r\n                      <td className=\"border border-gray-300 px-2 py-1\">\r\n                        <input\r\n                          type=\"date\"\r\n                          name=\"date\"\r\n                          value={editedMovement?.date.split('T')[0] || ''}\r\n                          onChange={handleEditInputChange}\r\n                          onClick={(e) => e.stopPropagation()}\r\n                          onFocus={(e) => e.target.select()}\r\n                          className=\"w-full px-2 py-1 text-sm border border-gray-400 rounded bg-white text-gray-900 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:outline-none cursor-pointer\"\r\n                        />\r\n                      </td>\r\n                      <td className=\"border border-gray-300 px-2 py-1 text-center\">\r\n                        {/* Calculated field, not editable */}\r\n                        {editedMovement?.stockedIn && editedMovement.stockedIn > 0\r\n                          ? editedMovement.stockedIn\r\n                          : editedMovement?.stockedOut || 0}\r\n                      </td>\r\n                      <td className=\"border border-gray-300 px-2 py-1\">\r\n                        <input\r\n                          type=\"number\"\r\n                          name=\"stockedIn\"\r\n                          min=\"0\"\r\n                          value={editedMovement?.stockedIn || 0}\r\n                          onChange={handleEditInputChange}\r\n                          onClick={(e) => e.stopPropagation()}\r\n                          onFocus={(e) => e.target.select()}\r\n                          className=\"w-full px-2 py-1 text-sm border border-gray-400 rounded bg-white text-gray-900 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:outline-none cursor-pointer\"\r\n                        />\r\n                      </td>\r\n                      <td className=\"border border-gray-300 px-2 py-1 text-center text-gray-500\">\r\n                        {/* Will be recalculated */}\r\n                        Auto\r\n                      </td>\r\n                      <td className=\"border border-gray-300 px-2 py-1\">\r\n                        <input\r\n                          type=\"number\"\r\n                          name=\"stockedOut\"\r\n                          min=\"0\"\r\n                          value={editedMovement?.stockedOut || 0}\r\n                          onChange={handleEditInputChange}\r\n                          onClick={(e) => e.stopPropagation()}\r\n                          onFocus={(e) => e.target.select()}\r\n                          className=\"w-full px-2 py-1 text-sm border border-gray-400 rounded bg-white text-gray-900 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:outline-none cursor-pointer\"\r\n                        />\r\n                      </td>\r\n                      <td className=\"border border-gray-300 px-2 py-1 text-center text-gray-500\">\r\n                        {/* Will be recalculated */}\r\n                        Auto\r\n                      </td>\r\n                      <td className=\"border border-gray-300 px-2 py-1\">\r\n                        <input\r\n                          type=\"text\"\r\n                          name=\"remarks\"\r\n                          value={editedMovement?.remarks || ''}\r\n                          onChange={handleEditInputChange}\r\n                          onClick={(e) => e.stopPropagation()}\r\n                          onFocus={(e) => e.target.select()}\r\n                          className=\"w-full px-2 py-1 text-sm border border-gray-400 rounded bg-white text-gray-900 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:outline-none cursor-text\"\r\n                        />\r\n                      </td>\r\n                      <td className=\"border border-gray-300 px-2 py-1\">\r\n                        <input\r\n                          type=\"text\"\r\n                          name=\"sign\"\r\n                          value={editedMovement?.sign || ''}\r\n                          onChange={handleEditInputChange}\r\n                          onClick={(e) => e.stopPropagation()}\r\n                          onFocus={(e) => e.target.select()}\r\n                          className=\"w-full px-2 py-1 text-sm border border-gray-400 rounded bg-white text-gray-900 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:outline-none cursor-text\"\r\n                        />\r\n                      </td>\r\n                      <td className=\"border border-gray-300 px-2 py-1\">\r\n                        <div className=\"flex space-x-1\">\r\n                          <button\r\n                            onClick={saveEditedMovement}\r\n                            disabled={isUpdating}\r\n                            className=\"bg-green-500 text-white px-2 py-1 rounded text-xs hover:bg-green-600\"\r\n                          >\r\n                            {isUpdating ? 'Saving...' : 'Save'}\r\n                          </button>\r\n                          <button\r\n                            onClick={cancelEditing}\r\n                            className=\"bg-gray-300 text-gray-700 px-2 py-1 rounded text-xs hover:bg-gray-400\"\r\n                          >\r\n                            Cancel\r\n                          </button>\r\n                        </div>\r\n                      </td>\r\n                    </tr>\r\n                  ) : (\r\n                    // Normal display row\r\n                    <tr key={movement.id || index} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>\r\n                      <td className=\"border border-gray-300 px-4 py-2 text-gray-900 font-medium\">{format(parseISO(movement.date), \"MMM dd, yyyy\")}</td>\r\n                      <td className=\"border border-gray-300 px-4 py-2 text-center text-gray-900\">\r\n                        {movement.stockedIn > 0 ? movement.stockedIn : movement.stockedOut}\r\n                      </td>\r\n                      <td className=\"border border-gray-300 px-4 py-2 text-center text-gray-900\">\r\n                        {movement.stockedIn > 0 ? movement.stockedIn : '-'}\r\n                      </td>\r\n                      <td className=\"border border-gray-300 px-4 py-2 text-center text-gray-900 font-medium\">{movement.totalStock}</td>\r\n                      <td className=\"border border-gray-300 px-4 py-2 text-center text-gray-900\">\r\n                        {movement.stockedOut > 0 ? movement.stockedOut : '-'}\r\n                      </td>\r\n                      <td className=\"border border-gray-300 px-4 py-2 text-center text-gray-900 font-medium\">{movement.balance}</td>\r\n                      <td className=\"border border-gray-300 px-4 py-2 text-gray-900\">{movement.remarks}</td>\r\n                      <td className=\"border border-gray-300 px-4 py-2 text-gray-900\">{movement.sign || '-'}</td>\r\n                      <td className=\"border border-gray-300 px-4 py-2\">\r\n                        <button\r\n                          onClick={() => startEditingMovement(movement)}\r\n                          className=\"bg-blue-500 text-white px-2 py-1 rounded text-xs hover:bg-blue-600\"\r\n                        >\r\n                          Edit\r\n                        </button>\r\n                      </td>\r\n                    </tr>\r\n                  )\r\n                ))}\r\n              </tbody>\r\n            </table>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Add Movement Button */}\r\n      <div className=\"mb-6\">\r\n        <motion.button\r\n          whileHover={{ scale: 1.02 }}\r\n          whileTap={{ scale: 0.98 }}\r\n          onClick={() => setShowAddMovementForm(!showAddMovementForm)}\r\n          className=\"bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 hover:shadow-md transition-all\"\r\n        >\r\n          {showAddMovementForm ? 'Cancel' : 'Add Stock Movement'}\r\n        </motion.button>\r\n      </div>\r\n\r\n      {/* Add Movement Form */}\r\n      {showAddMovementForm && (\r\n        <div className=\"bg-gray-50 p-6 rounded-lg shadow-sm border border-gray-200 mb-6\">\r\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Add Stock Movement</h3>\r\n\r\n          <form onSubmit={handleAddMovement} className=\"space-y-4\">\r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n              {/* Date */}\r\n              <div>\r\n                <label htmlFor=\"date\" className=\"block text-sm font-medium text-gray-800 mb-1\">\r\n                  Date\r\n                </label>\r\n                <input\r\n                  id=\"date\"\r\n                  type=\"date\"\r\n                  name=\"date\"\r\n                  value={newMovement.date}\r\n                  onChange={handleInputChange}\r\n                  className=\"w-full px-4 py-2 border border-gray-400 rounded-lg bg-gray-50 text-gray-900 focus:ring-blue-500 focus:border-blue-500\"\r\n                  required\r\n                />\r\n              </div>\r\n\r\n              {/* Stocked In */}\r\n              <div>\r\n                <label htmlFor=\"stockedIn\" className=\"block text-sm font-medium text-gray-800 mb-1\">\r\n                  Stocked In\r\n                </label>\r\n                <input\r\n                  id=\"stockedIn\"\r\n                  type=\"number\"\r\n                  name=\"stockedIn\"\r\n                  min=\"0\"\r\n                  value={newMovement.stockedIn}\r\n                  onChange={handleInputChange}\r\n                  className=\"w-full px-4 py-2 border border-gray-400 rounded-lg bg-gray-50 text-gray-900 focus:ring-blue-500 focus:border-blue-500\"\r\n                />\r\n              </div>\r\n\r\n              {/* Stocked Out */}\r\n              <div>\r\n                <label htmlFor=\"stockedOut\" className=\"block text-sm font-medium text-gray-800 mb-1\">\r\n                  Stocked Out\r\n                </label>\r\n                <input\r\n                  id=\"stockedOut\"\r\n                  type=\"number\"\r\n                  name=\"stockedOut\"\r\n                  min=\"0\"\r\n                  value={newMovement.stockedOut}\r\n                  onChange={handleInputChange}\r\n                  className=\"w-full px-4 py-2 border border-gray-400 rounded-lg bg-gray-50 text-gray-900 focus:ring-blue-500 focus:border-blue-500\"\r\n                />\r\n              </div>\r\n\r\n              {/* Sign */}\r\n              <div>\r\n                <label htmlFor=\"sign\" className=\"block text-sm font-medium text-gray-800 mb-1\">\r\n                  Sign\r\n                </label>\r\n                <input\r\n                  id=\"sign\"\r\n                  type=\"text\"\r\n                  name=\"sign\"\r\n                  value={newMovement.sign}\r\n                  onChange={handleInputChange}\r\n                  className=\"w-full px-4 py-2 border border-gray-400 rounded-lg bg-gray-50 text-gray-900 focus:ring-blue-500 focus:border-blue-500\"\r\n                  placeholder=\"Your name\"\r\n                />\r\n              </div>\r\n            </div>\r\n\r\n            {/* Remarks */}\r\n            <div>\r\n              <label htmlFor=\"remarks\" className=\"block text-sm font-medium text-gray-800 mb-1\">\r\n                Remarks\r\n              </label>\r\n              <textarea\r\n                id=\"remarks\"\r\n                name=\"remarks\"\r\n                rows={2}\r\n                value={newMovement.remarks}\r\n                onChange={handleInputChange}\r\n                className=\"w-full px-4 py-2 border border-gray-400 rounded-lg bg-gray-50 text-gray-900 focus:ring-blue-500 focus:border-blue-500\"\r\n                placeholder=\"Any additional notes about this movement\"\r\n              />\r\n            </div>\r\n\r\n            {/* Submit Button */}\r\n            <div className=\"flex justify-end\">\r\n              <motion.button\r\n                type=\"submit\"\r\n                disabled={isSubmitting}\r\n                whileHover={{ scale: 1.02 }}\r\n                whileTap={{ scale: 0.98 }}\r\n                className={`bg-blue-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-blue-700 hover:shadow-md transition-all ${isSubmitting ? 'opacity-70 cursor-not-allowed' : ''\r\n                  }`}\r\n              >\r\n                {isSubmitting ? (\r\n                  <div className=\"flex items-center\">\r\n                    <SpinningLoader size=\"small\" className=\"mr-2\" />\r\n                    <span>Adding...</span>\r\n                  </div>\r\n                ) : (\r\n                  'Add Movement'\r\n                )}\r\n              </motion.button>\r\n            </div>\r\n          </form>\r\n        </div>\r\n      )}\r\n\r\n      {/* Delete Confirmation Modal */}\r\n      {showDeleteModal && (\r\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\r\n          <div className=\"bg-white rounded-lg p-6 max-w-md w-full\">\r\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Delete Stock Product</h3>\r\n            <p className=\"text-gray-700 mb-6\">\r\n              Are you sure you want to delete <span className=\"font-semibold\">{stockProduct.name}</span>? This action cannot be undone.\r\n            </p>\r\n            <div className=\"flex justify-end gap-3\">\r\n              <button\r\n                onClick={() => setShowDeleteModal(false)}\r\n                className=\"px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50\"\r\n              >\r\n                Cancel\r\n              </button>\r\n              <button\r\n                onClick={handleDelete}\r\n                disabled={isDeleting}\r\n                className=\"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700\"\r\n              >\r\n                {isDeleting ? (\r\n                  <div className=\"flex items-center\">\r\n                    <SpinningLoader size=\"small\" className=\"mr-2\" />\r\n                    <span>Deleting...</span>\r\n                  </div>\r\n                ) : (\r\n                  'Delete'\r\n                )}\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AADA;AAAA;AAJA;AANA;;;;;;;;;;;;;AA6Ce,SAAS;IACtB,0CAA0C;IAC1C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,YAAY,QAAQ;IAE1B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IACtE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC1E,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB;QACpE,MAAM,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC5C,WAAW;QACX,YAAY;QACZ,SAAS;QACT,MAAM;IACR;IACA,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IAC3E,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE5C,2BAA2B;IAC3B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,oBAAoB;YACxB,IAAI;gBACF,WAAW;gBAEX,IAAI;oBACF,MAAM,WAAW,MAAM,sHAAA,CAAA,YAAS,CAAC,WAAW,CAC1C,sHAAA,CAAA,iBAAc,CAAC,UAAU,EACzB,sHAAA,CAAA,iBAAc,CAAC,yBAAyB,EACxC;oBAGF,MAAM,mBAAmB;oBAEzB,oCAAoC;oBACpC,IAAI,CAAC,iBAAiB,cAAc,EAAE;wBACpC,iBAAiB,cAAc,GAAG,EAAE;oBACtC;oBAEA,kDAAkD;oBAClD,MAAM,sBAAsB,MAAM,OAAO,CAAC,iBAAiB,cAAc,IACrE,iBAAiB,cAAc,GAC/B,EAAE;oBAEN,MAAM,uBAAuB,oBAAoB,GAAG,CAAC,CAAC,UAAU;wBAC9D,IAAI,OAAO,aAAa,UAAU;4BAChC,IAAI;gCACF,MAAM,iBAAiB,KAAK,KAAK,CAAC;gCAClC,wDAAwD;gCACxD,OAAO;oCAAE,GAAG,cAAc;oCAAE,IAAI,CAAC,SAAS,EAAE,OAAO;gCAAC;4BACtD,EAAE,OAAO,OAAO;gCACd,QAAQ,KAAK,CAAC,iCAAiC;gCAC/C,OAAO;4BACT;wBACF;wBACA,0CAA0C;wBAC1C,OAAO;4BAAE,GAAG,QAAQ;4BAAE,IAAI,CAAC,SAAS,EAAE,OAAO;wBAAC;oBAChD,GAAG,MAAM,CAAC,UAAU,yBAAyB;oBAE7C,kDAAkD;oBAClD,MAAM,wBAAsC;wBAC1C,KAAK,AAAC,iBAAiB,GAAG,IAAe;wBACzC,MAAM,AAAC,iBAAiB,IAAI,IAAe;wBAC3C,YAAY,AAAC,iBAAiB,UAAU,IAAe,IAAI,OAAO,WAAW;wBAC7E,aAAa,AAAC,iBAAiB,WAAW,IAAe,IAAI,OAAO,WAAW;wBAC/E,gBAAgB;oBAClB;oBAEA,gBAAgB;gBAClB,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,iCAAiC;oBAE/C,6CAA6C;oBAC7C,MAAM,sBAAuC;wBAC3C;4BACE,MAAM;4BACN,WAAW;4BACX,YAAY;4BACZ,SAAS;4BACT,YAAY;4BACZ,SAAS;4BACT,MAAM;wBACR;wBACA;4BACE,MAAM;4BACN,WAAW;4BACX,YAAY;4BACZ,SAAS;4BACT,YAAY;4BACZ,SAAS;4BACT,MAAM;wBACR;wBACA;4BACE,MAAM;4BACN,WAAW;4BACX,YAAY;4BACZ,SAAS;4BACT,YAAY;4BACZ,SAAS;4BACT,MAAM;wBACR;qBACD;oBAED,MAAM,eAA6B;wBACjC,KAAK;wBACL,MAAM,cAAc,MAAM,gBAAgB,cAAc,MAAM,eAAe;wBAC7E,gBAAgB;wBAChB,aAAa;wBACb,YAAY;oBACd;oBAEA,gBAAgB;oBAChB,MAAM,OAAO,+CAA+C;gBAC9D;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iCAAiC;gBAC/C,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;gBACZ,OAAO,IAAI,CAAC;YACd,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG;QAAC;QAAW;KAAO;IAEtB,6CAA6C;IAC7C,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,eAAe,CAAA,OAAQ,CAAC;gBACtB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE,SAAS,eAAe,SAAS,eAAe,OAAO,SAAS;YAC1E,CAAC;IACH;IAEA,gDAAgD;IAChD,MAAM,wBAAwB,CAAC;QAC7B,IAAI,CAAC,gBAAgB;QAErB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAEhC,kBAAkB,CAAA;YAChB,IAAI,CAAC,MAAM,OAAO;YAClB,OAAO;gBACL,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE,SAAS,eAAe,SAAS,eAAe,OAAO,SAAS;YAC1E;QACF;IACF;IAEA,2BAA2B;IAC3B,MAAM,uBAAuB,CAAC;QAC5B,qBAAqB,SAAS,EAAE,IAAI;QACpC,kBAAkB;YAAE,GAAG,QAAQ;QAAC;QAChC,uBAAuB,QAAQ,yBAAyB;IAC1D;IAEA,iBAAiB;IACjB,MAAM,gBAAgB;QACpB,qBAAqB;QACrB,kBAAkB;IACpB;IAEA,uBAAuB;IACvB,MAAM,qBAAqB;QACzB,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,mBAAmB;QAE5D,IAAI;YACF,cAAc;YAEd,8CAA8C;YAC9C,MAAM,gBAAgB,aAAa,cAAc,CAAC,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YAC1E,IAAI,kBAAkB,CAAC,GAAG;gBACxB,MAAM,IAAI,MAAM;YAClB;YAEA,iCAAiC;YACjC,MAAM,mBAAmB;mBAAI,aAAa,cAAc;aAAC;YACzD,gBAAgB,CAAC,cAAc,GAAG;YAElC,yEAAyE;YACzE,IAAK,IAAI,IAAI,eAAe,IAAI,iBAAiB,MAAM,EAAE,IAAK;gBAC5D,IAAI,MAAM,GAAG;oBACX,iBAAiB;oBACjB,gBAAgB,CAAC,EAAE,CAAC,UAAU,GAAG,gBAAgB,CAAC,EAAE,CAAC,SAAS;oBAC9D,gBAAgB,CAAC,EAAE,CAAC,OAAO,GAAG,gBAAgB,CAAC,EAAE,CAAC,UAAU,GAAG,gBAAgB,CAAC,EAAE,CAAC,UAAU;gBAC/F,OAAO;oBACL,uBAAuB;oBACvB,MAAM,eAAe,gBAAgB,CAAC,IAAI,EAAE;oBAC5C,gBAAgB,CAAC,EAAE,CAAC,UAAU,GAAG,aAAa,OAAO,GAAG,gBAAgB,CAAC,EAAE,CAAC,SAAS;oBACrF,gBAAgB,CAAC,EAAE,CAAC,OAAO,GAAG,gBAAgB,CAAC,EAAE,CAAC,UAAU,GAAG,gBAAgB,CAAC,EAAE,CAAC,UAAU;gBAC/F;YACF;YAEA,4CAA4C;YAC5C,MAAM,yBAAyB,iBAAiB,GAAG,CAAC,CAAA;gBAClD,+DAA+D;gBAC/D,6DAA6D;gBAC7D,MAAM,EAAE,EAAE,EAAE,GAAG,mBAAmB,GAAG;gBACrC,OAAO,KAAK,SAAS,CAAC;YACxB;YAEA,qBAAqB;YACrB,MAAM,sHAAA,CAAA,YAAS,CAAC,cAAc,CAC5B,sHAAA,CAAA,iBAAc,CAAC,UAAU,EACzB,sHAAA,CAAA,iBAAc,CAAC,yBAAyB,EACxC,WACA;gBACE,gBAAgB;gBAChB,aAAa,IAAI,OAAO,WAAW;YACrC;YAGF,qBAAqB;YACrB,MAAM,sBAAsB;gBAC1B,GAAG,YAAY;gBACf,gBAAgB;gBAChB,aAAa,IAAI,OAAO,WAAW;YACrC;YAEA,gBAAgB;YAChB,qBAAqB;YACrB,kBAAkB;YAClB,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,cAAc;QAChB;IACF;IAEA,mCAAmC;IACnC,MAAM,oBAAoB,OAAO;QAC/B,EAAE,cAAc;QAEhB,IAAI,CAAC,cAAc;QAEnB,IAAI;YACF,gBAAgB;YAEhB,uBAAuB;YACvB,MAAM,eAAe,aAAa,cAAc,CAAC,aAAa,cAAc,CAAC,MAAM,GAAG,EAAE;YACxF,MAAM,gBAAgB,aAAa,UAAU,GAAG,YAAY,SAAS;YACrE,MAAM,aAAa,gBAAgB,YAAY,UAAU;YAEzD,4BAA4B;YAC5B,MAAM,gBAA+B;gBACnC,MAAM,IAAI,KAAK,YAAY,IAAI,EAAE,WAAW;gBAC5C,WAAW,YAAY,SAAS;gBAChC,YAAY,YAAY,UAAU;gBAClC,SAAS,YAAY,OAAO;gBAC5B,YAAY;gBACZ,SAAS;gBACT,MAAM,YAAY,IAAI;YACxB;YAEA,kCAAkC;YAClC,MAAM,wBAAwB;mBAAI,aAAa,cAAc;gBAAE;aAAc;YAE7E,uCAAuC;YACvC,MAAM,sBAAsB,KAAK,SAAS,CAAC;YAE3C,kEAAkE;YAClE,IAAI,+BAAyC,EAAE;YAC/C,IAAI;gBACF,MAAM,aAAa,MAAM,sHAAA,CAAA,YAAS,CAAC,WAAW,CAC5C,sHAAA,CAAA,iBAAc,CAAC,UAAU,EACzB,sHAAA,CAAA,iBAAc,CAAC,yBAAyB,EACxC;gBAGF,+BAA+B,WAAW,cAAc,IAAI,EAAE;YAChE,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,2CAA2C;gBACzD,+BAA+B,EAAE;YACnC;YAEA,2CAA2C;YAC3C,MAAM,+BAA+B;mBAAI;gBAA8B;aAAoB;YAE3F,uBAAuB;YACvB,MAAM,sBAAsB;gBAC1B,GAAG,YAAY;gBACf,gBAAgB;gBAChB,aAAa,IAAI,OAAO,WAAW;YACrC;YAEA,IAAI;gBACF,MAAM,sHAAA,CAAA,YAAS,CAAC,cAAc,CAC5B,sHAAA,CAAA,iBAAc,CAAC,UAAU,EACzB,sHAAA,CAAA,iBAAc,CAAC,yBAAyB,EACxC,WACA;oBACE,gBAAgB;oBAChB,aAAa,IAAI,OAAO,WAAW;gBACrC;YAEJ,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,6CAA6C;gBAC3D,MAAM,OAAO,+CAA+C;YAC9D;YAEA,qBAAqB;YACrB,gBAAgB;YAEhB,aAAa;YACb,eAAe;gBACb,MAAM,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;gBAC5C,WAAW;gBACX,YAAY;gBACZ,SAAS;gBACT,MAAM;YACR;YAEA,uBAAuB;YACvB,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,gCAAgC;IAChC,MAAM,eAAe;QACnB,IAAI;YACF,cAAc;YAEd,IAAI;gBACF,MAAM,sHAAA,CAAA,YAAS,CAAC,cAAc,CAC5B,sHAAA,CAAA,iBAAc,CAAC,UAAU,EACzB,sHAAA,CAAA,iBAAc,CAAC,yBAAyB,EACxC;YAEJ,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,+CAA+C;gBAC7D,MAAM,OAAO,+CAA+C;YAC9D;YAEA,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;YAEd,sCAAsC;YACtC,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACZ,cAAc;YACd,mBAAmB;QACrB;IACF;IAEA,4BAA4B;IAC5B,MAAM,cAAc;QAClB,IAAI;YACF,IAAI,aAAa,OAAO,EAAE;gBACxB,qBAAqB;gBACrB,uJAAA,CAAA,UAAK,CAAC,OAAO,eACX,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,2IAAA,CAAA,UAAc;4BAAC,MAAK;;;;;;sCACrB,8OAAC;sCAAK;;;;;;;;;;;;gBAIV,+DAA+D;gBAC/D,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBAEjD,MAAM,UAAU,aAAa,OAAO;gBAEpC,oCAAoC;gBACpC,MAAM,SAAS,KAAK,CAAC,KAAK;gBAE1B,8BAA8B;gBAC9B,MAAM,SAAS,MAAM,IAAI,CAAC,QAAQ,oBAAoB,CAAC;gBACvD,MAAM,QAAQ,GAAG,CAAC,OAAO,GAAG,CAAC,CAAA;oBAC3B,IAAI,IAAI,QAAQ,EAAE,OAAO,QAAQ,OAAO;oBACxC,OAAO,IAAI,QAAQ,CAAA;wBACjB,IAAI,MAAM,GAAG;wBACb,IAAI,OAAO,GAAG;oBAChB;gBACF;gBAEA,8DAA8D;gBAC9D,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBAEjD,+CAA+C;gBAC/C,MAAM,mBAAmB,QAAQ,KAAK,CAAC,QAAQ;gBAC/C,MAAM,mBAAmB,QAAQ,KAAK,CAAC,QAAQ;gBAC/C,MAAM,eAAe,QAAQ,aAAa,CAAC;gBAC3C,MAAM,qBAAqB,cAAc,MAAM;gBAE/C,wCAAwC;gBACxC,QAAQ,KAAK,CAAC,QAAQ,GAAG;gBACzB,QAAQ,KAAK,CAAC,QAAQ,GAAG;gBACzB,IAAI,cAAc;oBAChB,aAAa,KAAK,CAAC,KAAK,GAAG;oBAC3B,aAAa,KAAK,CAAC,QAAQ,GAAG;gBAChC;gBAEA,6BAA6B;gBAC7B,MAAM,WAAW,CAAC,WAAW,EAAE,cAAc,KAAK,QAAQ,QAAQ,KAAK,IAAI,CAAC;gBAU5E,uCAAuC;gBACvC,MAAM,WAAW,OAAO,QAAQ;gBAChC,MAAM,cAAc;gBAEpB,8EAA8E;gBAC9E,YAAY,GAAG,CAAC;oBACd,QAAQ;wBAAC;wBAAG;wBAAG;wBAAG;qBAAE;oBACpB,UAAU;oBACV,OAAO;wBAAE,MAAM;wBAAQ,SAAS;oBAAK;oBACrC,aAAa;wBACX,OAAO;wBACP,SAAS;wBACT,SAAS;wBACT,iBAAiB;wBACjB,YAAY;wBACZ,iBAAiB;wBACjB,aAAa;wBACb,cAAc;wBACd,SAAS;wBACT,SAAS;wBACT,wBAAwB;wBACxB,YAAY,SAAU,MAAM;4BAC1B,QAAQ,GAAG,CAAC,oBAAoB,OAAO,KAAK,EAAE,KAAK,OAAO,MAAM;wBAClE;oBACF;oBACA,OAAO;wBACL,MAAM;wBACN,QAAQ;wBACR,aAAa;wBACb,UAAU;wBACV,WAAW;wBACX,UAAU;4BAAC;yBAAa,CAAC,yBAAyB;oBACpD;gBACF;gBAEA,MAAM,YAAY,IAAI,CAAC,SAAS,IAAI;gBAEpC,0BAA0B;gBAC1B,QAAQ,KAAK,CAAC,QAAQ,GAAG;gBACzB,QAAQ,KAAK,CAAC,QAAQ,GAAG;gBACzB,IAAI,gBAAgB,oBAAoB;oBACtC,aAAa,KAAK,CAAC,KAAK,GAAG;gBAC7B;gBAEA,uJAAA,CAAA,UAAK,CAAC,OAAO;gBACb,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;YAChB,OAAO;gBACL,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,uJAAA,CAAA,UAAK,CAAC,OAAO;YACb,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,2IAAA,CAAA,UAAc;gBAAC,MAAK;gBAAQ,MAAK;;;;;;;;;;;IAGxC;IAEA,IAAI,CAAC,cAAc;QACjB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAClC,8OAAC,4JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;wBACV,OAAO;4BAAE,yBAAyB;wBAAc;;0CAEhD,8OAAC;gCACC,OAAM;gCACN,WAAU;gCACV,MAAK;gCACL,SAAQ;gCACR,QAAO;0CAEP,cAAA,8OAAC;oCACC,eAAc;oCACd,gBAAe;oCACf,aAAa;oCACb,GAAE;;;;;;;;;;;4BAEA;;;;;;;;;;;;;;;;;;IAMhB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,8HAAA,CAAA,UAAM;gBAAC,KAAI;;;;;;0BAGZ,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oBACH,MAAK;oBACL,WAAU;oBACV,OAAO;wBAAE,yBAAyB;oBAAc;;sCAEhD,8OAAC;4BACC,OAAM;4BACN,WAAU;4BACV,MAAK;4BACL,SAAQ;4BACR,QAAO;sCAEP,cAAA,8OAAC;gCACC,eAAc;gCACd,gBAAe;gCACf,aAAa;gCACb,GAAE;;;;;;;;;;;wBAEA;;;;;;;;;;;;0BAMV,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAK,WAAU;kDAAkC;;;;;;oCACjD,aAAa,IAAI;;;;;;;0CAEpB,8OAAC;gCAAE,WAAU;;oCAA6B;kDAC/B,8OAAC;wCAAK,WAAU;kDAAe,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,uIAAA,CAAA,WAAQ,AAAD,EAAE,aAAa,UAAU,GAAG;;;;;;oCAAuB;kDAAiB,8OAAC;wCAAK,WAAU;kDAAe,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,uIAAA,CAAA,WAAQ,AAAD,EAAE,aAAa,WAAW,GAAG;;;;;;;;;;;;;;;;;;kCAIxM,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;gCACxB,SAAS;gCACT,WAAU;0CACX;;;;;;0CAID,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;gCACxB,SAAS,IAAM,mBAAmB;gCAClC,WAAU;0CACX;;;;;;;;;;;;;;;;;;0BAOL,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,KAAK;oBAAc,WAAU;;sCAChC,8OAAC;sEAAc;;8CACb,8OAAC;8EAAc;8CACb,cAAA,8OAAC;kFAAc;;0DACb,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KAAI;gDACJ,KAAI;gDACJ,OAAO;gDACP,QAAQ;gDACR,WAAU;gDACV,OAAO;oDAAE,WAAW;gDAAU;;;;;;0DAEhC,8OAAC;;;kEACC,8OAAC;kGAAa;kEAA4D;;;;;;kEAC1E,8OAAC;kGAAa;kEAAsC;;;;;;;;;;;;;;;;;;;;;;;8CAI1D,8OAAC;8EAAc;;sDACb,8OAAC;sFAAc;sDACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KAAI;gDACJ,KAAI;gDACJ,OAAO;gDACP,QAAQ;gDACR,WAAU;gDACV,UAAU;;;;;;;;;;;sDAGd,8OAAC;sFAAc;;8DACb,8OAAC;8FAAc;8DAA8C;;;;;;8DAG7D,8OAAC;8FAAc;8DACZ,aAAa,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAwE1B,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAM,WAAU;;kDACf,8OAAC;kDACC,cAAA,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAG,WAAU;8DAA+D;;;;;;8DAC7E,8OAAC;oDAAG,WAAU;8DAA+D;;;;;;8DAC7E,8OAAC;oDAAG,WAAU;8DAA+D;;;;;;8DAC7E,8OAAC;oDAAG,WAAU;8DAA+D;;;;;;8DAC7E,8OAAC;oDAAG,WAAU;8DAA+D;;;;;;8DAC7E,8OAAC;oDAAG,WAAU;8DAA+D;;;;;;8DAC7E,8OAAC;oDAAG,WAAU;8DAA+D;;;;;;8DAC7E,8OAAC;oDAAG,WAAU;8DAA+D;;;;;;8DAC7E,8OAAC;oDAAG,WAAU;8DAA+D;;;;;;;;;;;;;;;;;kDAGjF,8OAAC;kDACE,aAAa,cAAc,CAAC,GAAG,CAAC,CAAC,UAAU,QAC1C,sBAAsB,SAAS,EAAE,GAC/B,gBAAgB;0DAChB,8OAAC;gDAEC,WAAU;gDACV,SAAS,CAAC,IAAM,EAAE,eAAe;;kEAEjC,8OAAC;wDAAG,WAAU;kEACZ,cAAA,8OAAC;4DACC,MAAK;4DACL,MAAK;4DACL,OAAO,gBAAgB,KAAK,MAAM,IAAI,CAAC,EAAE,IAAI;4DAC7C,UAAU;4DACV,SAAS,CAAC,IAAM,EAAE,eAAe;4DACjC,SAAS,CAAC,IAAM,EAAE,MAAM,CAAC,MAAM;4DAC/B,WAAU;;;;;;;;;;;kEAGd,8OAAC;wDAAG,WAAU;kEAEX,gBAAgB,aAAa,eAAe,SAAS,GAAG,IACrD,eAAe,SAAS,GACxB,gBAAgB,cAAc;;;;;;kEAEpC,8OAAC;wDAAG,WAAU;kEACZ,cAAA,8OAAC;4DACC,MAAK;4DACL,MAAK;4DACL,KAAI;4DACJ,OAAO,gBAAgB,aAAa;4DACpC,UAAU;4DACV,SAAS,CAAC,IAAM,EAAE,eAAe;4DACjC,SAAS,CAAC,IAAM,EAAE,MAAM,CAAC,MAAM;4DAC/B,WAAU;;;;;;;;;;;kEAGd,8OAAC;wDAAG,WAAU;kEACgB;;;;;;kEAG9B,8OAAC;wDAAG,WAAU;kEACZ,cAAA,8OAAC;4DACC,MAAK;4DACL,MAAK;4DACL,KAAI;4DACJ,OAAO,gBAAgB,cAAc;4DACrC,UAAU;4DACV,SAAS,CAAC,IAAM,EAAE,eAAe;4DACjC,SAAS,CAAC,IAAM,EAAE,MAAM,CAAC,MAAM;4DAC/B,WAAU;;;;;;;;;;;kEAGd,8OAAC;wDAAG,WAAU;kEACgB;;;;;;kEAG9B,8OAAC;wDAAG,WAAU;kEACZ,cAAA,8OAAC;4DACC,MAAK;4DACL,MAAK;4DACL,OAAO,gBAAgB,WAAW;4DAClC,UAAU;4DACV,SAAS,CAAC,IAAM,EAAE,eAAe;4DACjC,SAAS,CAAC,IAAM,EAAE,MAAM,CAAC,MAAM;4DAC/B,WAAU;;;;;;;;;;;kEAGd,8OAAC;wDAAG,WAAU;kEACZ,cAAA,8OAAC;4DACC,MAAK;4DACL,MAAK;4DACL,OAAO,gBAAgB,QAAQ;4DAC/B,UAAU;4DACV,SAAS,CAAC,IAAM,EAAE,eAAe;4DACjC,SAAS,CAAC,IAAM,EAAE,MAAM,CAAC,MAAM;4DAC/B,WAAU;;;;;;;;;;;kEAGd,8OAAC;wDAAG,WAAU;kEACZ,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEACC,SAAS;oEACT,UAAU;oEACV,WAAU;8EAET,aAAa,cAAc;;;;;;8EAE9B,8OAAC;oEACC,SAAS;oEACT,WAAU;8EACX;;;;;;;;;;;;;;;;;;+CAvFA,CAAC,KAAK,EAAE,SAAS,EAAE,EAAE;;;;uDA8F5B,qBAAqB;0DACrB,8OAAC;gDAA8B,WAAW,QAAQ,MAAM,IAAI,aAAa;;kEACvE,8OAAC;wDAAG,WAAU;kEAA8D,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,uIAAA,CAAA,WAAQ,AAAD,EAAE,SAAS,IAAI,GAAG;;;;;;kEAC5G,8OAAC;wDAAG,WAAU;kEACX,SAAS,SAAS,GAAG,IAAI,SAAS,SAAS,GAAG,SAAS,UAAU;;;;;;kEAEpE,8OAAC;wDAAG,WAAU;kEACX,SAAS,SAAS,GAAG,IAAI,SAAS,SAAS,GAAG;;;;;;kEAEjD,8OAAC;wDAAG,WAAU;kEAA0E,SAAS,UAAU;;;;;;kEAC3G,8OAAC;wDAAG,WAAU;kEACX,SAAS,UAAU,GAAG,IAAI,SAAS,UAAU,GAAG;;;;;;kEAEnD,8OAAC;wDAAG,WAAU;kEAA0E,SAAS,OAAO;;;;;;kEACxG,8OAAC;wDAAG,WAAU;kEAAkD,SAAS,OAAO;;;;;;kEAChF,8OAAC;wDAAG,WAAU;kEAAkD,SAAS,IAAI,IAAI;;;;;;kEACjF,8OAAC;wDAAG,WAAU;kEACZ,cAAA,8OAAC;4DACC,SAAS,IAAM,qBAAqB;4DACpC,WAAU;sEACX;;;;;;;;;;;;+CAnBI,SAAS,EAAE,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAiCtC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oBACZ,YAAY;wBAAE,OAAO;oBAAK;oBAC1B,UAAU;wBAAE,OAAO;oBAAK;oBACxB,SAAS,IAAM,uBAAuB,CAAC;oBACvC,WAAU;8BAET,sBAAsB,WAAW;;;;;;;;;;;YAKrC,qCACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA2C;;;;;;kCAEzD,8OAAC;wBAAK,UAAU;wBAAmB,WAAU;;0CAC3C,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;;0DACC,8OAAC;gDAAM,SAAQ;gDAAO,WAAU;0DAA+C;;;;;;0DAG/E,8OAAC;gDACC,IAAG;gDACH,MAAK;gDACL,MAAK;gDACL,OAAO,YAAY,IAAI;gDACvB,UAAU;gDACV,WAAU;gDACV,QAAQ;;;;;;;;;;;;kDAKZ,8OAAC;;0DACC,8OAAC;gDAAM,SAAQ;gDAAY,WAAU;0DAA+C;;;;;;0DAGpF,8OAAC;gDACC,IAAG;gDACH,MAAK;gDACL,MAAK;gDACL,KAAI;gDACJ,OAAO,YAAY,SAAS;gDAC5B,UAAU;gDACV,WAAU;;;;;;;;;;;;kDAKd,8OAAC;;0DACC,8OAAC;gDAAM,SAAQ;gDAAa,WAAU;0DAA+C;;;;;;0DAGrF,8OAAC;gDACC,IAAG;gDACH,MAAK;gDACL,MAAK;gDACL,KAAI;gDACJ,OAAO,YAAY,UAAU;gDAC7B,UAAU;gDACV,WAAU;;;;;;;;;;;;kDAKd,8OAAC;;0DACC,8OAAC;gDAAM,SAAQ;gDAAO,WAAU;0DAA+C;;;;;;0DAG/E,8OAAC;gDACC,IAAG;gDACH,MAAK;gDACL,MAAK;gDACL,OAAO,YAAY,IAAI;gDACvB,UAAU;gDACV,WAAU;gDACV,aAAY;;;;;;;;;;;;;;;;;;0CAMlB,8OAAC;;kDACC,8OAAC;wCAAM,SAAQ;wCAAU,WAAU;kDAA+C;;;;;;kDAGlF,8OAAC;wCACC,IAAG;wCACH,MAAK;wCACL,MAAM;wCACN,OAAO,YAAY,OAAO;wCAC1B,UAAU;wCACV,WAAU;wCACV,aAAY;;;;;;;;;;;;0CAKhB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,MAAK;oCACL,UAAU;oCACV,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;oCACxB,WAAW,CAAC,yGAAyG,EAAE,eAAe,kCAAkC,IACpK;8CAEH,6BACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,2IAAA,CAAA,UAAc;gDAAC,MAAK;gDAAQ,WAAU;;;;;;0DACvC,8OAAC;0DAAK;;;;;;;;;;;+CAGR;;;;;;;;;;;;;;;;;;;;;;;YASX,iCACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA2C;;;;;;sCACzD,8OAAC;4BAAE,WAAU;;gCAAqB;8CACA,8OAAC;oCAAK,WAAU;8CAAiB,aAAa,IAAI;;;;;;gCAAQ;;;;;;;sCAE5F,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS,IAAM,mBAAmB;oCAClC,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,SAAS;oCACT,UAAU;oCACV,WAAU;8CAET,2BACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,2IAAA,CAAA,UAAc;gDAAC,MAAK;gDAAQ,WAAU;;;;;;0DACvC,8OAAC;0DAAK;;;;;;;;;;;+CAGR;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASlB"}}, {"offset": {"line": 1652, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}