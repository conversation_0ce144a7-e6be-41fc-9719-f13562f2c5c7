module.exports = {

"[project]/src/lib/appwrite.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "account": (()=>account),
    "appwriteConfig": (()=>appwriteConfig),
    "databases": (()=>databases),
    "storage": (()=>storage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$appwrite$2f$dist$2f$esm$2f$sdk$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/appwrite/dist/esm/sdk.js [app-ssr] (ecmascript)");
;
// Initialize the Appwrite client
const client = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$appwrite$2f$dist$2f$esm$2f$sdk$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Client"]().setEndpoint("https://cloud.appwrite.io/v1").setProject("67d07dc9000bafdd5d81"); // Confirmed correct project ID
const account = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$appwrite$2f$dist$2f$esm$2f$sdk$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Account"](client);
const databases = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$appwrite$2f$dist$2f$esm$2f$sdk$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Databases"](client);
const storage = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$appwrite$2f$dist$2f$esm$2f$sdk$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Storage"](client);
const appwriteConfig = {
    // Using the confirmed database ID
    databaseId: "6813eadb003e7d64f63c",
    productsCollectionId: "6813eaf40036e52c29b1",
    categoriesCollectionId: "6817640f000dd0b67c77",
    stockProductsCollectionId: "681a651d001cc3de8395",
    stockMovementsCollectionId: "681bddcc000204a3748d",
    storageId: "6813ea36001624c1202a"
}; // project id: 67d07d7b0010f39ec77d
 // database id: 67d8833d000778157021
 // collection id: 67d8835b002502c5d7ba
 // storage id: 67d8841a001213adf116
}}),
"[project]/src/app/Components/SpinningLoader.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-ssr] (ecmascript)");
"use client";
;
;
const SpinningLoader = ({ size = 'medium', className = '', text })=>{
    // Size mapping
    const sizeMap = {
        small: 'w-6 h-6 border-2',
        medium: 'w-10 h-10 border-3',
        large: 'w-16 h-16 border-4'
    };
    const sizeClass = sizeMap[size];
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `flex flex-col items-center justify-center ${className}`,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                className: `${sizeClass} rounded-full border-t-pink-500 border-r-blue-500 border-b-pink-500 border-l-blue-500`,
                animate: {
                    rotate: 360
                },
                transition: {
                    duration: 1.5,
                    repeat: Infinity,
                    ease: "linear"
                },
                style: {
                    borderStyle: 'solid'
                }
            }, void 0, false, {
                fileName: "[project]/src/app/Components/SpinningLoader.tsx",
                lineNumber: 28,
                columnNumber: 7
            }, this),
            text && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                className: "mt-3 text-sm text-gray-600 font-medium",
                children: text
            }, void 0, false, {
                fileName: "[project]/src/app/Components/SpinningLoader.tsx",
                lineNumber: 39,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/Components/SpinningLoader.tsx",
        lineNumber: 27,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = SpinningLoader;
}}),
"[project]/src/app/admin/receipt-sender/page.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$script$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/script.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/client/app-dir/link.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$appwrite$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/lib/appwrite.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$appwrite$2f$dist$2f$esm$2f$sdk$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/appwrite/dist/esm/sdk.js [app-ssr] (ecmascript)");
// Link is used in the JSX
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$Components$2f$SpinningLoader$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/app/Components/SpinningLoader.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/image.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/react-hot-toast/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-ssr] (ecmascript)");
"use client";
;
;
;
;
;
;
;
// Define a fixed document ID for the receipt counter
const RECEIPT_COUNTER_ID = 'receipt-counter';
// Use the dedicated Receipt ID collection
const RECEIPT_COLLECTION_ID = '681721d5001b6819df1b';
// Use the PDF storage collection
const PDF_COLLECTION_ID = '6817243c001593c1b882';
;
;
;
const ReceiptSender = ()=>{
    const [formData, setFormData] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({
        customerName: '',
        whatsapp: '',
        items: [
            {
                description: '',
                quantity: 1,
                unitPrice: 0,
                total: 0
            }
        ],
        subtotal: 0,
        amountPaid: 0,
        balance: 0,
        receiptNumber: '',
        date: new Date().toISOString().split('T')[0],
        receiptIdNumber: 0
    });
    const [showPreview, setShowPreview] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isLoadingId, setIsLoadingId] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [startingIdNumber, setStartingIdNumber] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('');
    const [showIdSettings, setShowIdSettings] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [showSizeModal, setShowSizeModal] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [receiptSize, setReceiptSize] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('big');
    const receiptRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    // Function to fetch the current receipt ID from Appwrite
    const fetchReceiptId = async ()=>{
        try {
            setIsLoadingId(true);
            console.log("=== RECEIPT ID FETCH START ===");
            console.log("Database ID:", __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$appwrite$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["appwriteConfig"].databaseId);
            console.log("Collection ID:", RECEIPT_COLLECTION_ID);
            console.log("Document ID:", RECEIPT_COUNTER_ID);
            // Try to get the receipt counter document directly
            try {
                console.log("Attempting to get document with ID:", RECEIPT_COUNTER_ID);
                const counterDoc = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$appwrite$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["databases"].getDocument(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$appwrite$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["appwriteConfig"].databaseId, RECEIPT_COLLECTION_ID, RECEIPT_COUNTER_ID);
                console.log("Found receipt counter document:", counterDoc);
                console.log("Raw receiptId value:", counterDoc.receiptId);
                console.log("receiptId type:", typeof counterDoc.receiptId);
                // Clear indication if receipt ID is coming through from Appwrite
                console.log("✅ RECEIPT ID FROM APPWRITE:", counterDoc.receiptId ? "YES - Value: " + counterDoc.receiptId : "NO - Value is missing or null");
                // Convert string ID to number, fallback to 1000 if not a valid number
                let currentId = counterDoc.receiptId ? parseInt(counterDoc.receiptId, 10) : 1000;
                console.log("Parsed receiptId to number:", currentId);
                // Use 1000 as fallback if parsing results in NaN
                currentId = isNaN(currentId) ? 1000 : currentId;
                console.log("Final currentId after validation:", currentId);
                // Generate the receipt number with the CEP prefix
                const newReceiptNumber = `CEP${currentId}`;
                console.log("Generated receipt number:", newReceiptNumber);
                setFormData((prev)=>({
                        ...prev,
                        receiptNumber: newReceiptNumber,
                        receiptIdNumber: currentId
                    }));
                console.log("Form data updated with receipt number");
                return;
            } catch (getError) {
                console.error('Error getting receipt counter document:', getError);
                console.log("Error details:", JSON.stringify(getError));
                console.log("Will create a new receipt counter document");
            }
            // If we get here, the document doesn't exist
            // Create the document with a default value
            try {
                const defaultId = 1000;
                console.log("Creating new receipt counter document with default ID:", defaultId);
                console.log("Document data to be created:", {
                    receiptId: defaultId.toString()
                });
                const createResponse = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$appwrite$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["databases"].createDocument(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$appwrite$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["appwriteConfig"].databaseId, RECEIPT_COLLECTION_ID, RECEIPT_COUNTER_ID, {
                    receiptId: defaultId.toString()
                });
                console.log("Document creation successful:", createResponse);
                console.log("Created document ID:", createResponse.$id);
                console.log("Created document receiptId:", createResponse.receiptId);
                // Clear indication if receipt ID was successfully stored in Appwrite
                console.log("✅ RECEIPT ID STORED IN APPWRITE:", createResponse.receiptId ? "YES - Value: " + createResponse.receiptId : "NO - Value is missing or null");
                // Set the receipt number with the default ID
                setFormData((prev)=>({
                        ...prev,
                        receiptNumber: `CEP${defaultId}`,
                        receiptIdNumber: defaultId
                    }));
                console.log("Form data updated with default receipt number:", `CEP${defaultId}`);
                return;
            } catch (createError) {
                console.error('Error creating receipt counter document:', createError);
                console.log("Error details:", JSON.stringify(createError, null, 2));
                throw createError; // Re-throw to be caught by the outer catch
            }
        } catch (error) {
            console.error('Error in receipt ID generation:', error);
            console.log("Error type:", typeof error);
            console.log("Error details:", JSON.stringify(error, null, 2));
            // Fallback to timestamp-based ID if there's an error
            const timestamp = new Date().getTime().toString().slice(-6);
            console.log("Using fallback timestamp-based ID:", timestamp);
            setFormData((prev)=>({
                    ...prev,
                    receiptNumber: `CEP${timestamp}`
                }));
            console.log("Form data updated with fallback receipt number:", `CEP${timestamp}`);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].error('Using fallback receipt ID. Please check Appwrite setup.');
        } finally{
            setIsLoadingId(false);
            console.log("=== RECEIPT ID FETCH COMPLETE ===");
        }
    };
    // Function to update the receipt ID in Appwrite
    const updateReceiptId = async (newId)=>{
        try {
            console.log("=== UPDATING RECEIPT ID ===");
            console.log("Attempting to update receipt ID to:", newId);
            // First check if the document exists
            try {
                const existingDoc = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$appwrite$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["databases"].getDocument(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$appwrite$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["appwriteConfig"].databaseId, RECEIPT_COLLECTION_ID, RECEIPT_COUNTER_ID);
                console.log("Found existing receipt counter document:", existingDoc);
                // If it exists, update it
                console.log("Updating document with new receiptId:", newId.toString());
                const updateResponse = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$appwrite$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["databases"].updateDocument(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$appwrite$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["appwriteConfig"].databaseId, RECEIPT_COLLECTION_ID, RECEIPT_COUNTER_ID, {
                    receiptId: newId.toString()
                });
                console.log("Update successful:", updateResponse);
                console.log("✅ RECEIPT ID UPDATED IN APPWRITE:", updateResponse.receiptId);
                return true;
            } catch (getError) {
                console.error('Document not found, creating new one:', getError);
                // If document doesn't exist, create it
                console.log("Creating new receipt counter document with ID:", newId.toString());
                const createResponse = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$appwrite$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["databases"].createDocument(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$appwrite$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["appwriteConfig"].databaseId, RECEIPT_COLLECTION_ID, RECEIPT_COUNTER_ID, {
                    receiptId: newId.toString()
                });
                console.log("Document creation successful:", createResponse);
                console.log("✅ RECEIPT ID CREATED IN APPWRITE:", createResponse.receiptId);
                return true;
            }
        } catch (error) {
            console.error('Error updating receipt ID:', error);
            console.log("Error details:", JSON.stringify(error, null, 2));
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].error('Failed to update receipt ID. Please try again.');
            return false;
        }
    };
    // Function to generate a new receipt ID
    const generateNewReceiptId = async ()=>{
        try {
            setIsLoadingId(true);
            let currentId = 1000;
            // Try to get the current ID
            console.log("=== GENERATE NEW RECEIPT ID ===");
            console.log("Attempting to get document with ID:", RECEIPT_COUNTER_ID);
            try {
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$appwrite$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["databases"].getDocument(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$appwrite$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["appwriteConfig"].databaseId, RECEIPT_COLLECTION_ID, RECEIPT_COUNTER_ID);
                console.log("getDocument response:", response);
                console.log("Raw receiptId from getDocument:", response.receiptId);
                console.log("receiptId type:", typeof response.receiptId);
                // Clear indication if receipt ID is coming through from Appwrite in generateNewReceiptId
                console.log("✅ RECEIPT ID FROM APPWRITE (generateNewReceiptId):", response.receiptId ? "YES - Value: " + response.receiptId : "NO - Value is missing or null");
                // Get the current ID if document exists and convert from string to number
                const idFromDb = response.receiptId ? parseInt(response.receiptId, 10) : 1000;
                console.log("Parsed receiptId to number:", idFromDb);
                // Use 1000 as fallback if parsing results in NaN
                currentId = isNaN(idFromDb) ? 1000 : idFromDb;
                console.log("Final currentId after validation:", currentId);
            } catch (getError) {
                console.error('Error getting receipt counter, using default:', getError);
                console.log("Error details:", JSON.stringify(getError, null, 2));
            // If document doesn't exist, we'll use the default 1000
            }
            // Increment the ID
            const newId = currentId + 1;
            console.log("New ID after increment:", newId);
            // Update the ID in Appwrite
            console.log("Updating receipt ID in Appwrite to:", newId);
            const updated = await updateReceiptId(newId);
            console.log("Update result:", updated);
            if (updated) {
                // Update the receipt number in the form
                const newReceiptNumber = `CEP${newId}`;
                console.log("Setting new receipt number:", newReceiptNumber);
                setFormData((prev)=>({
                        ...prev,
                        receiptNumber: newReceiptNumber,
                        receiptIdNumber: newId
                    }));
                console.log("Form data updated with new receipt number");
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].success('Generated new receipt ID');
            } else {
                console.log("Failed to update receipt ID");
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].error('Failed to update receipt ID');
            }
        } catch (error) {
            console.error('Error generating new receipt ID:', error);
            console.log("Error details:", JSON.stringify(error, null, 2));
            // Generate a fallback ID based on timestamp
            const timestamp = new Date().getTime().toString().slice(-6);
            const fallbackId = `CEP${timestamp}`;
            console.log("Using fallback timestamp-based ID:", fallbackId);
            setFormData((prev)=>({
                    ...prev,
                    receiptNumber: fallbackId
                }));
            console.log("Form data updated with fallback receipt number");
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].error('Using fallback receipt ID due to error');
        } finally{
            setIsLoadingId(false);
            console.log("=== GENERATE NEW RECEIPT ID COMPLETE ===");
        }
    };
    // Function to set a custom starting ID
    const setCustomStartingId = async ()=>{
        if (startingIdNumber === '' || isNaN(Number(startingIdNumber))) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].error('Please enter a valid number');
            return;
        }
        const newId = Number(startingIdNumber);
        if (newId < 1000) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].error('ID must be at least 1000');
            return;
        }
        try {
            setIsLoadingId(true);
            console.log("=== SETTING CUSTOM RECEIPT ID ===");
            console.log("Setting custom receipt ID to:", newId);
            // Update the ID in Appwrite
            const updated = await updateReceiptId(newId);
            console.log("Update result:", updated);
            if (updated) {
                // Update the receipt number in the form
                const newReceiptNumber = `CEP${newId}`;
                console.log("Setting new receipt number:", newReceiptNumber);
                setFormData((prev)=>({
                        ...prev,
                        receiptNumber: newReceiptNumber,
                        receiptIdNumber: newId
                    }));
                console.log("Form data updated with custom receipt number");
                setStartingIdNumber('');
                setShowIdSettings(false);
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].success(`Receipt ID set to ${newId}`);
            } else {
                console.log("Failed to update receipt ID");
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].error('Failed to update receipt ID');
            }
        } catch (error) {
            console.error('Error setting custom receipt ID:', error);
            console.log("Error details:", JSON.stringify(error, null, 2));
            // Generate a fallback ID based on the custom number
            const fallbackId = `CEP${newId}`;
            console.log("Using fallback custom ID:", fallbackId);
            setFormData((prev)=>({
                    ...prev,
                    receiptNumber: fallbackId,
                    receiptIdNumber: newId
                }));
            console.log("Form data updated with fallback custom receipt number");
            setStartingIdNumber('');
            setShowIdSettings(false);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].error('Using custom ID locally only. Database update failed.');
        } finally{
            setIsLoadingId(false);
            console.log("=== CUSTOM RECEIPT ID SETTING COMPLETE ===");
        }
    };
    // Function to initialize the receipt ID if it doesn't exist
    const initializeReceiptId = async ()=>{
        try {
            console.log("=== INITIALIZING RECEIPT ID ===");
            console.log("Checking if receipt counter document exists...");
            try {
                // Try to get the existing document
                const existingDoc = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$appwrite$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["databases"].getDocument(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$appwrite$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["appwriteConfig"].databaseId, RECEIPT_COLLECTION_ID, RECEIPT_COUNTER_ID);
                console.log("Receipt counter document exists:", existingDoc);
                console.log("Current receiptId:", existingDoc.receiptId);
                // If it exists, just fetch it
                fetchReceiptId();
            } catch  {
                console.log("Receipt counter document doesn't exist, creating it...");
                // Create the document with a default value
                const defaultId = 1000;
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$appwrite$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["databases"].createDocument(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$appwrite$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["appwriteConfig"].databaseId, RECEIPT_COLLECTION_ID, RECEIPT_COUNTER_ID, {
                    receiptId: defaultId.toString()
                });
                console.log("Receipt counter document created with default ID:", defaultId);
                // Now fetch it
                fetchReceiptId();
            }
        } catch (error) {
            console.error("Error initializing receipt ID:", error);
            // Still try to fetch in case there's a document
            fetchReceiptId();
        }
    };
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        // Log Appwrite configuration for debugging
        console.log("=== APPWRITE CONFIGURATION ===");
        console.log("Database ID:", __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$appwrite$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["appwriteConfig"].databaseId);
        console.log("Products Collection ID:", __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$appwrite$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["appwriteConfig"].productsCollectionId);
        console.log("Storage ID:", __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$appwrite$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["appwriteConfig"].storageId);
        console.log("Receipt Counter ID:", RECEIPT_COUNTER_ID);
        console.log("Receipt Collection ID:", RECEIPT_COLLECTION_ID);
        // Initialize the receipt ID when the component mounts
        initializeReceiptId();
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);
    // Real-time calculations are now handled directly in the change handlers
    const handleItemChange = (index, field, value)=>{
        // Create a copy of the items array
        const newItems = [
            ...formData.items
        ];
        // Update the specific field
        newItems[index] = {
            ...newItems[index],
            [field]: value
        };
        // Calculate the total for this item immediately
        if (field === 'quantity' || field === 'unitPrice') {
            newItems[index].total = Number(newItems[index].quantity) * Number(newItems[index].unitPrice);
        }
        // Update the form data with the new items
        setFormData((prev)=>{
            // Calculate new subtotal based on all items
            const subtotal = newItems.reduce((sum, item)=>sum + item.total, 0);
            // Calculate new balance based on the new subtotal and current amount paid
            const balance = subtotal - prev.amountPaid;
            // Return updated form data with new items, subtotal, and balance
            return {
                ...prev,
                items: newItems,
                subtotal,
                balance
            };
        });
    };
    const addLineItem = ()=>{
        setFormData((prev)=>{
            // Add new empty item
            const newItems = [
                ...prev.items,
                {
                    description: '',
                    quantity: 1,
                    unitPrice: 0,
                    total: 0
                }
            ];
            // No need to recalculate totals since the new item has total=0
            // But we'll do it anyway for consistency
            const subtotal = newItems.reduce((sum, item)=>sum + item.total, 0);
            const balance = subtotal - prev.amountPaid;
            return {
                ...prev,
                items: newItems,
                subtotal,
                balance
            };
        });
    };
    const removeLineItem = (index)=>{
        if (formData.items.length > 1) {
            const newItems = formData.items.filter((_, i)=>i !== index);
            // Update form data and recalculate totals
            setFormData((prev)=>{
                // Calculate new subtotal based on filtered items
                const subtotal = newItems.reduce((sum, item)=>sum + item.total, 0);
                // Calculate new balance
                const balance = subtotal - prev.amountPaid;
                return {
                    ...prev,
                    items: newItems,
                    subtotal,
                    balance
                };
            });
        }
    };
    const handleSubmit = (e)=>{
        e.preventDefault();
        setShowSizeModal(true);
    };
    // Handle receipt size selection
    const handleSizeSelection = (size)=>{
        setReceiptSize(size);
        setShowSizeModal(false);
        setShowPreview(true);
    };
    const handleChange = (e)=>{
        setFormData({
            ...formData,
            [e.target.name]: e.target.value
        });
    };
    const downloadPDF = async ()=>{
        try {
            if (typeof window.html2pdf === 'function' && receiptRef.current) {
                // Show loading toast with custom spinner
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].loading(/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-center gap-2",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$Components$2f$SpinningLoader$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                            size: "small"
                        }, void 0, false, {
                            fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                            lineNumber: 584,
                            columnNumber: 25
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            children: "Generating PDF..."
                        }, void 0, false, {
                            fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                            lineNumber: 585,
                            columnNumber: 25
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                    lineNumber: 583,
                    columnNumber: 21
                }, this));
                // Use a timeout to ensure the UI updates before PDF generation
                await new Promise((resolve)=>setTimeout(resolve, 100));
                const element = receiptRef.current;
                const fileName = `${formData.receiptNumber}_${formData.customerName.replace(/\s+/g, '_')}.pdf`;
                console.log("=== GENERATING PDF ===");
                console.log("File name:", fileName);
                // Generate PDF directly from the visible receipt element
                // This is more reliable than creating a clone
                try {
                    // Configure PDF options based on receipt size
                    const opt = receiptSize === 'mini' ? {
                        margin: [
                            3,
                            3,
                            3,
                            3
                        ],
                        filename: fileName,
                        image: {
                            type: 'jpeg',
                            quality: 0.95
                        },
                        html2canvas: {
                            scale: 2.5,
                            useCORS: true,
                            logging: true,
                            letterRendering: true,
                            allowTaint: true,
                            foreignObjectRendering: false,
                            scrollY: 0,
                            windowHeight: window.innerHeight * 2
                        },
                        jsPDF: {
                            unit: 'mm',
                            format: [
                                98,
                                225
                            ],
                            orientation: 'portrait',
                            compress: true
                        }
                    } : {
                        margin: [
                            5,
                            5,
                            5,
                            5
                        ],
                        filename: fileName,
                        image: {
                            type: 'jpeg',
                            quality: 0.95
                        },
                        html2canvas: {
                            scale: 1.5,
                            useCORS: true,
                            logging: true,
                            letterRendering: true,
                            allowTaint: true,
                            foreignObjectRendering: false,
                            scrollY: 0,
                            windowHeight: window.innerHeight * 2
                        },
                        jsPDF: {
                            unit: 'mm',
                            format: 'a4',
                            orientation: 'portrait',
                            compress: true
                        }
                    };
                    // Wait for fonts and images to load
                    await document.fonts.ready;
                    // Wait for all images to load
                    const images = Array.from(element.getElementsByTagName('img'));
                    await Promise.all(images.map((img)=>{
                        if (img.complete) return Promise.resolve();
                        return new Promise((resolve)=>{
                            img.onload = resolve;
                            img.onerror = resolve;
                        });
                    }));
                    // Force a small delay to ensure all styles are applied
                    await new Promise((resolve)=>setTimeout(resolve, 500));
                    // For mini receipts, temporarily force all text to black for better thermal printing
                    let originalColors = [];
                    if (receiptSize === 'mini') {
                        const allElements = element.querySelectorAll('*');
                        allElements.forEach((el)=>{
                            const htmlEl = el;
                            const computedStyle = window.getComputedStyle(htmlEl);
                            if (computedStyle.color && computedStyle.color !== 'rgb(0, 0, 0)') {
                                originalColors.push({
                                    element: htmlEl,
                                    originalColor: htmlEl.style.color || computedStyle.color
                                });
                                htmlEl.style.color = '#000000 !important';
                            }
                        });
                    }
                    // Generate PDF directly from the visible element
                    const pdfResult = await window.html2pdf().set(opt).from(element).output('blob');
                    // Restore original colors for mini receipts
                    if (receiptSize === 'mini') {
                        originalColors.forEach(({ element, originalColor })=>{
                            element.style.color = originalColor;
                        });
                    }
                    console.log("PDF generated successfully");
                    // Create a direct download link
                    const link = document.createElement('a');
                    const url = window.URL.createObjectURL(new Blob([
                        pdfResult
                    ], {
                        type: 'application/pdf'
                    }));
                    link.href = url;
                    link.download = fileName;
                    // Wait a moment before clicking to ensure everything is ready
                    await new Promise((resolve)=>setTimeout(resolve, 100));
                    link.click();
                    // Cleanup after a short delay
                    setTimeout(()=>{
                        window.URL.revokeObjectURL(url);
                    }, 100);
                    // Upload to Appwrite in the background
                    try {
                        console.log("=== UPLOADING PDF TO APPWRITE ===");
                        // Create File object
                        const file = new File([
                            pdfResult
                        ], fileName, {
                            type: 'application/pdf'
                        });
                        // Generate a unique file ID
                        const fileId = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$appwrite$2f$dist$2f$esm$2f$sdk$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ID"].unique();
                        console.log("File ID:", fileId);
                        // Upload to Appwrite with public read permission
                        const uploadedFile = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$appwrite$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["storage"].createFile(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$appwrite$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["appwriteConfig"].storageId, fileId, file, [
                            'read("any")'
                        ] // This makes the file publicly readable
                        );
                        console.log("File uploaded successfully:", uploadedFile);
                        // Get the file download URL
                        const fileUrl = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$appwrite$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["storage"].getFileDownload(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$appwrite$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["appwriteConfig"].storageId, uploadedFile.$id).toString();
                        console.log("File URL:", fileUrl);
                        // Store the PDF reference in the PDF collection using the exact attribute names
                        const pdfDocument = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$appwrite$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["databases"].createDocument(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$appwrite$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["appwriteConfig"].databaseId, PDF_COLLECTION_ID, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$appwrite$2f$dist$2f$esm$2f$sdk$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ID"].unique(), {
                            name: formData.customerName,
                            receiptId: formData.receiptNumber,
                            receiptPdf: fileUrl
                        });
                        console.log("PDF document created in database:", pdfDocument);
                        // Increment the receipt ID after successful upload
                        await incrementReceiptIdAfterUpload();
                    } catch (uploadError) {
                        console.error("Error uploading PDF to Appwrite:", uploadError);
                        console.log("Error details:", JSON.stringify(uploadError, null, 2));
                    // Don't show an error toast since this is a background operation
                    }
                    // Dismiss loading toast and show success
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].dismiss();
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].success('PDF generated successfully!');
                    return pdfResult;
                } catch (error) {
                    console.error('PDF Generation Error:', error);
                    console.log("Error details:", JSON.stringify(error, null, 2));
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].dismiss();
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].error('Failed to generate PDF');
                    throw error;
                }
            }
            throw new Error('PDF generation not available');
        } catch (error) {
            console.error('PDF Generation Error:', error);
            console.log("Error details:", JSON.stringify(error, null, 2));
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].dismiss();
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].error('Failed to generate PDF');
            throw error;
        }
    };
    // Function to check if a receipt PDF already exists in Appwrite
    const findExistingReceiptPDF = async (receiptNumber)=>{
        try {
            console.log("=== CHECKING FOR EXISTING RECEIPT PDF ===");
            console.log("Looking for receipt number:", receiptNumber);
            // Query the PDF collection for the receipt ID
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$appwrite$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["databases"].listDocuments(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$appwrite$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["appwriteConfig"].databaseId, PDF_COLLECTION_ID, [
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$appwrite$2f$dist$2f$esm$2f$sdk$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Query"].equal("receiptId", receiptNumber)
            ]);
            console.log("Search results:", response);
            if (response.documents.length > 0) {
                // Found existing receipt PDF
                const existingDoc = response.documents[0];
                console.log("Found existing receipt PDF:", existingDoc);
                return {
                    exists: true,
                    fileUrl: existingDoc.receiptPdf,
                    document: existingDoc
                };
            }
            // No existing receipt PDF found
            console.log("No existing receipt PDF found");
            return {
                exists: false
            };
        } catch (error) {
            console.error("Error checking for existing receipt PDF:", error);
            console.log("Error details:", JSON.stringify(error, null, 2));
            return {
                exists: false,
                error
            };
        }
    };
    // Function to increment receipt ID after successful upload
    const incrementReceiptIdAfterUpload = async ()=>{
        try {
            console.log("=== INCREMENTING RECEIPT ID AFTER UPLOAD ===");
            // Get the current receipt ID
            const currentIdNumber = formData.receiptIdNumber || 1000;
            const newId = currentIdNumber + 1;
            console.log("Current ID:", currentIdNumber);
            console.log("New ID:", newId);
            // Update the ID in Appwrite
            const updated = await updateReceiptId(newId);
            if (updated) {
                console.log("Receipt ID incremented successfully");
                // Update the form data with the new receipt ID
                setFormData((prev)=>({
                        ...prev,
                        receiptNumber: `CEP${newId}`,
                        receiptIdNumber: newId
                    }));
                console.log("Form data updated with new receipt ID");
                return true;
            } else {
                console.error("Failed to increment receipt ID");
                return false;
            }
        } catch (error) {
            console.error("Error incrementing receipt ID:", error);
            console.log("Error details:", JSON.stringify(error, null, 2));
            return false;
        }
    };
    // Function to generate PDF blob without opening it in a new tab
    const generatePDFBlobOnly = async ()=>{
        try {
            console.log("=== GENERATING PDF BLOB ONLY ===");
            if (typeof window.html2pdf === 'function' && receiptRef.current) {
                const element = receiptRef.current;
                const fileName = `${formData.receiptNumber}_${formData.customerName.replace(/\s+/g, '_')}.pdf`;
                console.log("Generating PDF blob for:", fileName);
                // Set options for the PDF based on receipt size
                const opt = receiptSize === 'mini' ? {
                    margin: [
                        3,
                        3,
                        3,
                        3
                    ],
                    filename: fileName,
                    image: {
                        type: 'jpeg',
                        quality: 0.95
                    },
                    html2canvas: {
                        scale: 2.5,
                        useCORS: true,
                        logging: true,
                        letterRendering: true,
                        allowTaint: true,
                        foreignObjectRendering: false,
                        scrollY: 0,
                        windowHeight: window.innerHeight * 2
                    },
                    jsPDF: {
                        unit: 'mm',
                        format: [
                            98,
                            225
                        ],
                        orientation: 'portrait',
                        compress: true
                    }
                } : {
                    margin: [
                        5,
                        5,
                        5,
                        5
                    ],
                    filename: fileName,
                    image: {
                        type: 'jpeg',
                        quality: 0.95
                    },
                    html2canvas: {
                        scale: 1.5,
                        useCORS: true,
                        logging: true,
                        letterRendering: true,
                        allowTaint: true,
                        foreignObjectRendering: false,
                        scrollY: 0,
                        windowHeight: window.innerHeight * 2
                    },
                    jsPDF: {
                        unit: 'mm',
                        format: 'a4',
                        orientation: 'portrait',
                        compress: true
                    }
                };
                // Wait for fonts and images to load
                await document.fonts.ready;
                // Wait for all images to load
                const images = Array.from(element.getElementsByTagName('img'));
                await Promise.all(images.map((img)=>{
                    if (img.complete) return Promise.resolve();
                    return new Promise((resolve)=>{
                        img.onload = resolve;
                        img.onerror = resolve;
                    });
                }));
                // Force a small delay to ensure all styles are applied
                await new Promise((resolve)=>setTimeout(resolve, 500));
                // For mini receipts, temporarily force all text to black for better thermal printing
                let originalColors = [];
                if (receiptSize === 'mini') {
                    const allElements = element.querySelectorAll('*');
                    allElements.forEach((el)=>{
                        const htmlEl = el;
                        const computedStyle = window.getComputedStyle(htmlEl);
                        if (computedStyle.color && computedStyle.color !== 'rgb(0, 0, 0)') {
                            originalColors.push({
                                element: htmlEl,
                                originalColor: htmlEl.style.color || computedStyle.color
                            });
                            htmlEl.style.color = '#000000 !important';
                        }
                    });
                }
                // Generate PDF blob
                const pdfBlob = await window.html2pdf().set(opt).from(element).output('blob');
                // Restore original colors for mini receipts
                if (receiptSize === 'mini') {
                    originalColors.forEach(({ element, originalColor })=>{
                        element.style.color = originalColor;
                    });
                }
                console.log("PDF blob generated successfully");
                return pdfBlob;
            }
            throw new Error('PDF generation not available');
        } catch (error) {
            console.error('Error generating PDF blob:', error);
            console.log("Error details:", JSON.stringify(error, null, 2));
            throw error;
        }
    };
    // Function to upload PDF to Appwrite and return the file URL
    const uploadPDFToAppwrite = async (pdfBlob)=>{
        try {
            console.log("=== UPLOADING PDF TO APPWRITE ===");
            // Create File object
            const fileName = `${formData.receiptNumber}_${formData.customerName.replace(/\s+/g, '_')}.pdf`;
            const file = new File([
                pdfBlob
            ], fileName, {
                type: 'application/pdf'
            });
            console.log("File name:", fileName);
            // Generate a unique file ID
            const fileId = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$appwrite$2f$dist$2f$esm$2f$sdk$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ID"].unique();
            console.log("File ID:", fileId);
            // Upload to Appwrite with public read permission
            const uploadedFile = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$appwrite$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["storage"].createFile(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$appwrite$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["appwriteConfig"].storageId, fileId, file, [
                'read("any")'
            ] // This makes the file publicly readable
            );
            console.log("File uploaded successfully:", uploadedFile);
            // Get the file download URL
            const fileUrl = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$appwrite$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["storage"].getFileDownload(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$appwrite$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["appwriteConfig"].storageId, uploadedFile.$id).toString();
            console.log("File URL:", fileUrl);
            // Store the PDF reference in the PDF collection using the exact attribute names
            const pdfDocument = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$appwrite$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["databases"].createDocument(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$appwrite$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["appwriteConfig"].databaseId, PDF_COLLECTION_ID, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$appwrite$2f$dist$2f$esm$2f$sdk$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ID"].unique(), {
                name: formData.customerName,
                receiptId: formData.receiptNumber,
                receiptPdf: fileUrl
            });
            console.log("PDF document created in database:", pdfDocument);
            // Increment the receipt ID after successful upload
            await incrementReceiptIdAfterUpload();
            return fileUrl;
        } catch (error) {
            console.error("Error uploading PDF to Appwrite:", error);
            console.log("Error details:", JSON.stringify(error, null, 2));
            throw error;
        }
    };
    const sendWhatsApp = async ()=>{
        try {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].loading(/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center gap-2",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$Components$2f$SpinningLoader$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                        size: "small"
                    }, void 0, false, {
                        fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                        lineNumber: 1026,
                        columnNumber: 21
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        children: "Preparing receipt for WhatsApp..."
                    }, void 0, false, {
                        fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                        lineNumber: 1027,
                        columnNumber: 21
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                lineNumber: 1025,
                columnNumber: 17
            }, this));
            let fileUrl = "";
            // First, check if the receipt PDF already exists in Appwrite
            const existingPDF = await findExistingReceiptPDF(formData.receiptNumber);
            if (existingPDF.exists && existingPDF.fileUrl) {
                // Use the existing PDF
                console.log("Using existing PDF file URL:", existingPDF.fileUrl);
                fileUrl = existingPDF.fileUrl;
                // No need to increment receipt ID since we're using an existing PDF
                console.log("Using existing PDF, not incrementing receipt ID");
            } else {
                // No existing PDF found, generate and upload a new one
                console.log("No existing PDF found, generating a new one");
                try {
                    // Generate the PDF blob without opening it
                    const pdfBlob = await generatePDFBlobOnly();
                    // Upload the PDF to Appwrite
                    fileUrl = await uploadPDFToAppwrite(pdfBlob);
                } catch (error) {
                    console.error("Error generating or uploading PDF:", error);
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].dismiss();
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].error('Failed to prepare receipt for WhatsApp');
                    return;
                }
            }
            // Share via WhatsApp
            const message = encodeURIComponent(`Hello ${formData.customerName}, here's your receipt ${formData.receiptNumber} for ₦${formData.subtotal.toLocaleString()}. Thank you for shopping with us!\n\nDownload Receipt: ${fileUrl}`);
            // Format the WhatsApp number
            let phone = formData.whatsapp.replace(/[^0-9]/g, '');
            // Add country code if not present
            if (!phone.startsWith('234') && !phone.startsWith('+234')) {
                // If the number starts with 0, replace it with 234
                if (phone.startsWith('0')) {
                    phone = '234' + phone.substring(1);
                } else {
                    // Otherwise, just add 234 prefix
                    phone = '234' + phone;
                }
            }
            // Remove + if present
            phone = phone.replace('+', '');
            console.log("Formatted WhatsApp number:", phone);
            // Create WhatsApp URL
            const whatsappUrl = `https://wa.me/${phone}?text=${message}`;
            console.log("WhatsApp URL:", whatsappUrl);
            // Dismiss loading toast
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].dismiss();
            // Open WhatsApp in a new tab
            window.open(whatsappUrl, '_blank');
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].success('Receipt sent to WhatsApp!');
        } catch (error) {
            console.error('Error sharing receipt:', error);
            console.log("Error details:", JSON.stringify(error, null, 2));
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].dismiss();
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].error('Failed to share receipt');
        }
    };
    const resetForm = async ()=>{
        // Generate a new receipt ID
        try {
            setIsLoadingId(true);
            console.log("=== RESET FORM - RECEIPT ID FETCH ===");
            let currentId = 1000;
            let newId = 1001;
            // Try to get the current ID
            console.log("Attempting to get document with ID:", RECEIPT_COUNTER_ID);
            try {
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$appwrite$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["databases"].getDocument(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$appwrite$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["appwriteConfig"].databaseId, RECEIPT_COLLECTION_ID, RECEIPT_COUNTER_ID);
                console.log("getDocument response in resetForm:", response);
                console.log("Raw receiptId from getDocument:", response.receiptId);
                console.log("receiptId type:", typeof response.receiptId);
                // Clear indication if receipt ID is coming through from Appwrite in resetForm
                console.log("✅ RECEIPT ID FROM APPWRITE (resetForm):", response.receiptId ? "YES - Value: " + response.receiptId : "NO - Value is missing or null");
                // Get the current ID if document exists and convert from string to number
                const idFromDb = response.receiptId ? parseInt(response.receiptId, 10) : 1000;
                console.log("Parsed receiptId to number:", idFromDb);
                // Use 1000 as fallback if parsing results in NaN
                currentId = isNaN(idFromDb) ? 1000 : idFromDb;
                console.log("Final currentId after validation:", currentId);
                // Increment the ID
                newId = currentId + 1;
                console.log("New ID after increment:", newId);
            } catch (getError) {
                console.error('Error getting receipt counter, using default:', getError);
                console.log("Error details:", JSON.stringify(getError, null, 2));
                console.log("Using default ID values");
            // If document doesn't exist, we'll use the default values
            }
            // Try to update the ID in Appwrite
            try {
                console.log("Updating receipt ID in Appwrite to:", newId);
                const updated = await updateReceiptId(newId);
                console.log("Update result:", updated);
            } catch (updateError) {
                console.error('Error updating receipt ID:', updateError);
                console.log("Error details:", JSON.stringify(updateError, null, 2));
                console.log("Continuing with the new ID even if update fails");
            // Continue with the new ID even if update fails
            }
            // Reset the form with the new receipt ID
            console.log("Resetting form with new receipt ID:", newId);
            setFormData({
                customerName: '',
                whatsapp: '',
                items: [
                    {
                        description: '',
                        quantity: 1,
                        unitPrice: 0,
                        total: 0
                    }
                ],
                subtotal: 0,
                amountPaid: 0,
                balance: 0,
                receiptNumber: `CEP${newId}`,
                receiptIdNumber: newId,
                date: new Date().toISOString().split('T')[0]
            });
            console.log("Form reset with new receipt number:", `CEP${newId}`);
        } catch (error) {
            console.error('Error generating new receipt ID:', error);
            console.log("Error details:", JSON.stringify(error, null, 2));
            // Fallback to timestamp-based ID
            const timestamp = new Date().getTime().toString().slice(-6);
            console.log("Using fallback timestamp-based ID:", timestamp);
            setFormData({
                customerName: '',
                whatsapp: '',
                items: [
                    {
                        description: '',
                        quantity: 1,
                        unitPrice: 0,
                        total: 0
                    }
                ],
                subtotal: 0,
                amountPaid: 0,
                balance: 0,
                receiptNumber: `CEP${timestamp}`,
                date: new Date().toISOString().split('T')[0]
            });
            console.log("Form reset with fallback receipt number:", `CEP${timestamp}`);
        } finally{
            setIsLoadingId(false);
            setShowPreview(false);
            console.log("=== RESET FORM COMPLETE ===");
        }
    };
    const startNewReceipt = ()=>{
        if (window.confirm('Are you sure you want to start a new receipt? This will clear the current one.')) {
            resetForm();
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "min-h-screen bg-gray-50 mt-32 pb-4 sm:pb-8 pt-6 sm:pt-8 md:pt-10",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$script$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                src: "https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"
            }, void 0, false, {
                fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                lineNumber: 1211,
                columnNumber: 13
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "max-w-7xl mx-auto px-3 sm:px-6 lg:px-8",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                        whileHover: {
                            scale: 1.05
                        },
                        whileTap: {
                            scale: 0.95
                        },
                        className: "inline-block mb-6",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                            href: "/admin",
                            className: "inline-flex items-center px-3 py-2 rounded-lg text-gray-700 hover:text-black hover:bg-gray-100 transition-all duration-200",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                    xmlns: "http://www.w3.org/2000/svg",
                                    className: "h-5 w-5 mr-2",
                                    fill: "none",
                                    viewBox: "0 0 24 24",
                                    stroke: "currentColor",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                        strokeLinecap: "round",
                                        strokeLinejoin: "round",
                                        strokeWidth: 2,
                                        d: "M10 19l-7-7m0 0l7-7m-7 7h18"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                        lineNumber: 1231,
                                        columnNumber: 29
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                    lineNumber: 1224,
                                    columnNumber: 25
                                }, this),
                                "Back to Admin"
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                            lineNumber: 1220,
                            columnNumber: 21
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                        lineNumber: 1215,
                        columnNumber: 17
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "mb-6 sm:mb-8",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                            className: "text-xl sm:text-2xl md:text-3xl font-bold text-gray-900",
                            children: "Receipt Generator"
                        }, void 0, false, {
                            fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                            lineNumber: 1243,
                            columnNumber: 21
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                        lineNumber: 1242,
                        columnNumber: 17
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "grid grid-cols-1 lg:grid-cols-2 gap-3 sm:gap-6 lg:items-start",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "bg-white p-4 sm:p-6 rounded-xl shadow-lg",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
                                    onSubmit: handleSubmit,
                                    className: "space-y-4",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "space-y-3 sm:space-y-4",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                            className: "block text-sm font-medium text-gray-700 mb-1",
                                                            children: "Customer Name"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                            lineNumber: 1251,
                                                            columnNumber: 37
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                            type: "text",
                                                            name: "customerName",
                                                            value: formData.customerName,
                                                            onChange: handleChange,
                                                            required: true,
                                                            className: "w-full px-3 sm:px-4 py-2 text-sm sm:text-base border border-gray-300 rounded-lg focus:ring-1 focus:ring-black focus:border-black text-gray-900 placeholder-gray-500"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                            lineNumber: 1254,
                                                            columnNumber: 37
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                    lineNumber: 1250,
                                                    columnNumber: 33
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                            className: "block text-sm font-medium text-gray-700 mb-1",
                                                            children: "Date"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                            lineNumber: 1265,
                                                            columnNumber: 37
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                            type: "date",
                                                            name: "date",
                                                            value: formData.date,
                                                            onChange: handleChange,
                                                            required: true,
                                                            className: "w-full px-3 sm:px-4 py-2 text-sm sm:text-base border border-gray-300 rounded-lg focus:ring-1 focus:ring-black focus:border-black text-gray-900"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                            lineNumber: 1268,
                                                            columnNumber: 37
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                    lineNumber: 1264,
                                                    columnNumber: 33
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                            className: "block text-sm font-medium text-gray-700 mb-1",
                                                            children: "WhatsApp Number"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                            lineNumber: 1279,
                                                            columnNumber: 37
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                            type: "tel",
                                                            name: "whatsapp",
                                                            value: formData.whatsapp,
                                                            onChange: handleChange,
                                                            required: true,
                                                            placeholder: "+234...",
                                                            className: "w-full px-3 sm:px-4 py-2 text-sm sm:text-base border border-gray-300 rounded-lg focus:ring-1 focus:ring-black focus:border-black text-gray-900 placeholder-gray-500"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                            lineNumber: 1282,
                                                            columnNumber: 37
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                    lineNumber: 1278,
                                                    columnNumber: 33
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "flex justify-between items-center mb-1",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                                    className: "block text-sm font-medium text-gray-700",
                                                                    children: "Receipt Number"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                    lineNumber: 1295,
                                                                    columnNumber: 41
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                                    type: "button",
                                                                    onClick: ()=>setShowIdSettings(!showIdSettings),
                                                                    className: "text-xs text-blue-600 hover:text-blue-800",
                                                                    children: showIdSettings ? 'Hide Settings' : 'ID Settings'
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                    lineNumber: 1298,
                                                                    columnNumber: 41
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                            lineNumber: 1294,
                                                            columnNumber: 37
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "relative",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                                    type: "text",
                                                                    name: "receiptNumber",
                                                                    value: formData.receiptNumber,
                                                                    readOnly: true,
                                                                    className: "w-full px-3 sm:px-4 py-2 text-sm sm:text-base border border-gray-300 rounded-lg focus:ring-1 focus:ring-black focus:border-black text-gray-900 placeholder-gray-500 bg-gray-50"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                    lineNumber: 1308,
                                                                    columnNumber: 41
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                                    type: "button",
                                                                    onClick: generateNewReceiptId,
                                                                    disabled: isLoadingId,
                                                                    className: "absolute right-2 top-1/2 transform -translate-y-1/2 text-xs bg-gray-100 hover:bg-gray-200 text-gray-700 px-2 py-1 rounded",
                                                                    children: isLoadingId ? 'Loading...' : 'Generate New ID'
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                    lineNumber: 1315,
                                                                    columnNumber: 41
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                            lineNumber: 1307,
                                                            columnNumber: 37
                                                        }, this),
                                                        showIdSettings && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "mt-2 p-3 border border-gray-200 rounded-lg bg-gray-50",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                    className: "text-xs text-gray-600 mb-2",
                                                                    children: "Set a custom starting ID number:"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                    lineNumber: 1327,
                                                                    columnNumber: 45
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "flex gap-2",
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                                            type: "number",
                                                                            value: startingIdNumber,
                                                                            onChange: (e)=>setStartingIdNumber(e.target.value === '' ? '' : Number(e.target.value)),
                                                                            placeholder: "e.g. 1000",
                                                                            min: "1000",
                                                                            className: "flex-1 px-3 py-1 text-sm border border-gray-300 rounded-lg"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                            lineNumber: 1329,
                                                                            columnNumber: 49
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                                            type: "button",
                                                                            onClick: setCustomStartingId,
                                                                            disabled: isLoadingId,
                                                                            className: "text-xs bg-blue-600 hover:bg-blue-700 text-white px-2 py-1 rounded flex items-center justify-center",
                                                                            children: isLoadingId ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                className: "flex items-center gap-1",
                                                                                children: [
                                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$Components$2f$SpinningLoader$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                                                        size: "small"
                                                                                    }, void 0, false, {
                                                                                        fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                                        lineNumber: 1345,
                                                                                        columnNumber: 61
                                                                                    }, this),
                                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                                        children: "Setting..."
                                                                                    }, void 0, false, {
                                                                                        fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                                        lineNumber: 1346,
                                                                                        columnNumber: 61
                                                                                    }, this)
                                                                                ]
                                                                            }, void 0, true, {
                                                                                fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                                lineNumber: 1344,
                                                                                columnNumber: 57
                                                                            }, this) : 'Set ID'
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                            lineNumber: 1337,
                                                                            columnNumber: 49
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                    lineNumber: 1328,
                                                                    columnNumber: 45
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                    className: "text-xs text-gray-500 mt-1",
                                                                    children: [
                                                                        "Current ID: ",
                                                                        formData.receiptIdNumber || 'Not set'
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                    lineNumber: 1351,
                                                                    columnNumber: 45
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                            lineNumber: 1326,
                                                            columnNumber: 41
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                    lineNumber: 1293,
                                                    columnNumber: 33
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                            lineNumber: 1249,
                                            columnNumber: 29
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "space-y-3 sm:space-y-4",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "flex justify-between items-center",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                            className: "text-base sm:text-lg text-gray-700 font-semibold",
                                                            children: "Items"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                            lineNumber: 1359,
                                                            columnNumber: 37
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                            type: "button",
                                                            onClick: addLineItem,
                                                            className: "text-xs sm:text-sm bg-gray-700 text-white px-2 sm:px-3 py-1 rounded-lg hover:bg-gray-600",
                                                            children: "+ Add Item"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                            lineNumber: 1360,
                                                            columnNumber: 37
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                    lineNumber: 1358,
                                                    columnNumber: 33
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "space-y-2 -mx-2 px-2 max-w-full overflow-x-auto",
                                                    children: formData.items.map((item, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "flex items-center gap-1.5 sm:gap-2 min-w-max sm:min-w-0",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                                    type: "text",
                                                                    placeholder: "Description",
                                                                    value: item.description,
                                                                    onChange: (e)=>handleItemChange(index, 'description', e.target.value),
                                                                    className: "flex-1 min-w-[100px] px-2 py-2 text-xs sm:text-sm border border-gray-300 rounded-lg focus:ring-1 focus:ring-black focus:border-black text-gray-900 placeholder-gray-500"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                    lineNumber: 1372,
                                                                    columnNumber: 45
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                                    type: "number",
                                                                    placeholder: "Qty",
                                                                    value: item.quantity === 0 ? '' : item.quantity,
                                                                    onChange: (e)=>{
                                                                        const value = e.target.value === '' ? 0 : Number(e.target.value);
                                                                        handleItemChange(index, 'quantity', value);
                                                                    },
                                                                    onFocus: (e)=>{
                                                                        if (e.target.value === '1' || e.target.value === '0') {
                                                                            e.target.select();
                                                                        }
                                                                    },
                                                                    className: "w-14 sm:w-20 px-1 sm:px-3 py-2 text-xs sm:text-sm border border-gray-300 rounded-lg focus:ring-1 focus:ring-black focus:border-black text-gray-900 placeholder-gray-500",
                                                                    min: "1",
                                                                    step: "1",
                                                                    inputMode: "numeric"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                    lineNumber: 1380,
                                                                    columnNumber: 45
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                                    type: "number",
                                                                    placeholder: "Price ₦",
                                                                    value: item.unitPrice === 0 ? '' : item.unitPrice,
                                                                    onChange: (e)=>{
                                                                        const value = e.target.value === '' ? 0 : Number(e.target.value);
                                                                        handleItemChange(index, 'unitPrice', value);
                                                                    },
                                                                    onFocus: (e)=>e.target.select(),
                                                                    className: "w-16 sm:w-24 px-1 sm:px-3 py-2 text-xs sm:text-sm border border-gray-300 rounded-lg focus:ring-1 focus:ring-black focus:border-black text-gray-900 placeholder-gray-500",
                                                                    min: "0",
                                                                    step: "1",
                                                                    inputMode: "numeric"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                    lineNumber: 1398,
                                                                    columnNumber: 45
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "flex items-center gap-1 sm:gap-2 min-w-[70px] sm:min-w-[100px] justify-end",
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                            className: "text-xs sm:text-sm text-gray-900 whitespace-nowrap font-medium transition-all duration-300",
                                                                            style: {
                                                                                color: item.total > 0 ? '#000' : '#888'
                                                                            },
                                                                            children: [
                                                                                "₦",
                                                                                item.total.toLocaleString()
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                            lineNumber: 1413,
                                                                            columnNumber: 49
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                                            type: "button",
                                                                            onClick: ()=>removeLineItem(index),
                                                                            className: "p-1 text-red-500 hover:text-red-700 rounded-full hover:bg-red-50",
                                                                            "aria-label": "Remove item",
                                                                            title: "Remove item",
                                                                            children: "×"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                            lineNumber: 1416,
                                                                            columnNumber: 49
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                    lineNumber: 1412,
                                                                    columnNumber: 45
                                                                }, this)
                                                            ]
                                                        }, index, true, {
                                                            fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                            lineNumber: 1371,
                                                            columnNumber: 41
                                                        }, this))
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                    lineNumber: 1369,
                                                    columnNumber: 33
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                            lineNumber: 1357,
                                            columnNumber: 29
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                            className: "block text-sm font-medium text-gray-700 mb-1",
                                                            children: "Amount Paid (₦)"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                            lineNumber: 1433,
                                                            columnNumber: 37
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                            type: "number",
                                                            placeholder: "Enter amount paid",
                                                            value: formData.amountPaid === 0 ? '' : formData.amountPaid,
                                                            onChange: (e)=>{
                                                                const value = e.target.value === '' ? 0 : Number(e.target.value);
                                                                setFormData((prev)=>({
                                                                        ...prev,
                                                                        amountPaid: value,
                                                                        balance: prev.subtotal - value // Calculate balance in real-time
                                                                    }));
                                                            },
                                                            onFocus: (e)=>e.target.select(),
                                                            className: "w-full px-3 sm:px-4 py-2 text-sm sm:text-base border rounded-lg text-gray-900 placeholder-gray-500",
                                                            min: "0",
                                                            step: "1"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                            lineNumber: 1436,
                                                            columnNumber: 37
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                    lineNumber: 1432,
                                                    columnNumber: 33
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "text-right space-y-2 text-gray-900 font-medium text-sm sm:text-base",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            className: "transition-all duration-300",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                    className: "inline-block w-20 text-gray-600",
                                                                    children: "Subtotal:"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                    lineNumber: 1456,
                                                                    columnNumber: 41
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                    className: "font-semibold",
                                                                    children: [
                                                                        "₦",
                                                                        formData.subtotal.toLocaleString()
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                    lineNumber: 1457,
                                                                    columnNumber: 41
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                            lineNumber: 1455,
                                                            columnNumber: 37
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            className: "transition-all duration-300",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                    className: "inline-block w-20 text-gray-600",
                                                                    children: "Balance:"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                    lineNumber: 1460,
                                                                    columnNumber: 41
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                    className: `font-semibold ${formData.balance > 0 ? 'text-red-600' : 'text-green-600'}`,
                                                                    children: [
                                                                        "₦",
                                                                        formData.balance.toLocaleString()
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                    lineNumber: 1461,
                                                                    columnNumber: 41
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                            lineNumber: 1459,
                                                            columnNumber: 37
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                    lineNumber: 1454,
                                                    columnNumber: 33
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                            lineNumber: 1431,
                                            columnNumber: 29
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex justify-between gap-4 mt-6",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                    type: "submit",
                                                    className: "w-full sm:flex-1 bg-[#333333] text-white px-4 py-2 rounded-lg hover:bg-gray-800 text-sm sm:text-base",
                                                    children: "Generate Receipt"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                    lineNumber: 1468,
                                                    columnNumber: 33
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                    type: "button",
                                                    onClick: resetForm,
                                                    className: "w-full sm:flex-1 px-4 py-2 border-gray-400 text-gray-600 border-2 rounded-lg hover:bg-gray-600 hover:text-white text-sm sm:text-base",
                                                    children: "Reset"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                    lineNumber: 1474,
                                                    columnNumber: 33
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                            lineNumber: 1467,
                                            columnNumber: 29
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                    lineNumber: 1248,
                                    columnNumber: 25
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                lineNumber: 1247,
                                columnNumber: 21
                            }, this),
                            showPreview && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "bg-white p-2 sm:p-4 md:p-6 rounded-xl shadow-lg overflow-auto",
                                style: {
                                    maxHeight: 'calc(100vh - 180px)',
                                    height: 'auto',
                                    overscrollBehavior: 'contain'
                                },
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        ref: receiptRef,
                                        className: `bg-white mx-auto ${receiptSize === 'mini' ? 'scale-100' : 'sm:scale-90 md:scale-85'}`,
                                        style: receiptSize === 'mini' ? {
                                            // Mini Receipt Styles (Envelope #9 compatible) - Optimized for thermal printing
                                            width: '280px',
                                            maxWidth: '280px',
                                            height: 'auto',
                                            padding: '16px',
                                            boxSizing: 'border-box',
                                            backgroundColor: 'white',
                                            fontFamily: 'Arial, sans-serif',
                                            fontSize: '18px',
                                            fontWeight: '700',
                                            lineHeight: '1.5',
                                            border: '1px solid #ddd',
                                            position: 'relative',
                                            margin: '0 auto',
                                            color: '#000000',
                                            pageBreakInside: 'avoid'
                                        } : {
                                            // Big Receipt Styles (A4)
                                            width: '100%',
                                            maxWidth: '210mm',
                                            height: 'auto',
                                            padding: '5mm',
                                            boxSizing: 'border-box',
                                            backgroundColor: 'white',
                                            fontFamily: 'Arial, sans-serif',
                                            fontSize: '10pt',
                                            lineHeight: '1.4',
                                            letterSpacing: '0.2px',
                                            border: '1px solid #eee',
                                            position: 'relative',
                                            margin: '0 auto',
                                            transformOrigin: 'top center',
                                            color: '#000000',
                                            pageBreakInside: 'avoid'
                                        },
                                        children: receiptSize === 'mini' ? // MINI RECEIPT LAYOUT (58mm thermal printer)
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "text-center mb-4",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                            src: "/logo.png",
                                                            alt: "Cepoka Logo",
                                                            width: 50,
                                                            height: 50,
                                                            className: "mx-auto mb-2",
                                                            style: {
                                                                maxWidth: '50px',
                                                                height: 'auto',
                                                                display: 'block',
                                                                margin: '0 auto 10px auto'
                                                            },
                                                            priority: true,
                                                            unoptimized: true
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                            lineNumber: 1537,
                                                            columnNumber: 45
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                                            style: {
                                                                fontSize: '20px',
                                                                fontWeight: '800',
                                                                color: '#000000',
                                                                marginBottom: '6px',
                                                                textAlign: 'center',
                                                                letterSpacing: '0.5px'
                                                            },
                                                            children: "CEPOKA BEAUTY HUB"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                            lineNumber: 1552,
                                                            columnNumber: 45
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            style: {
                                                                fontSize: '14px',
                                                                fontWeight: '600',
                                                                color: '#000000',
                                                                textAlign: 'center',
                                                                marginBottom: '10px',
                                                                lineHeight: '1.4'
                                                            },
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    children: "Lekki, Lagos"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                    lineNumber: 1570,
                                                                    columnNumber: 49
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    children: "+234 803 123 4567"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                    lineNumber: 1571,
                                                                    columnNumber: 49
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                            lineNumber: 1562,
                                                            columnNumber: 45
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                    lineNumber: 1536,
                                                    columnNumber: 41
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    style: {
                                                        marginBottom: '14px',
                                                        fontSize: '15px',
                                                        fontWeight: '600',
                                                        color: '#000000',
                                                        borderTop: '2px dashed #000',
                                                        borderBottom: '2px dashed #000',
                                                        padding: '8px 0' // More padding
                                                    },
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            style: {
                                                                marginBottom: '4px',
                                                                lineHeight: '1.4'
                                                            },
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                                    children: "Customer:"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                    lineNumber: 1586,
                                                                    columnNumber: 49
                                                                }, this),
                                                                " ",
                                                                formData.customerName
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                            lineNumber: 1585,
                                                            columnNumber: 45
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            style: {
                                                                marginBottom: '4px',
                                                                lineHeight: '1.4'
                                                            },
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                                    children: "Receipt #:"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                    lineNumber: 1589,
                                                                    columnNumber: 49
                                                                }, this),
                                                                " ",
                                                                formData.receiptNumber
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                            lineNumber: 1588,
                                                            columnNumber: 45
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            style: {
                                                                marginBottom: '4px',
                                                                lineHeight: '1.4'
                                                            },
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                                    children: "Tel:"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                    lineNumber: 1592,
                                                                    columnNumber: 49
                                                                }, this),
                                                                " ",
                                                                formData.whatsapp
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                            lineNumber: 1591,
                                                            columnNumber: 45
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            style: {
                                                                lineHeight: '1.4'
                                                            },
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                                    children: "Date:"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                    lineNumber: 1595,
                                                                    columnNumber: 49
                                                                }, this),
                                                                " ",
                                                                new Date(formData.date).toLocaleDateString('en-NG', {
                                                                    year: 'numeric',
                                                                    month: 'short',
                                                                    day: 'numeric'
                                                                })
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                            lineNumber: 1594,
                                                            columnNumber: 45
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                    lineNumber: 1576,
                                                    columnNumber: 41
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    style: {
                                                        marginBottom: '14px'
                                                    },
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            style: {
                                                                fontSize: '14px',
                                                                fontWeight: '800',
                                                                textAlign: 'center',
                                                                marginBottom: '8px',
                                                                color: '#000000',
                                                                letterSpacing: '0.5px'
                                                            },
                                                            children: "CASH SALES INVOICE"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                            lineNumber: 1605,
                                                            columnNumber: 45
                                                        }, this),
                                                        formData.items.map((item, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                style: {
                                                                    marginBottom: '6px',
                                                                    fontSize: '12px',
                                                                    fontWeight: '600',
                                                                    color: '#000000',
                                                                    borderBottom: index < formData.items.length - 1 ? '1px dotted #666' : 'none',
                                                                    paddingBottom: '6px'
                                                                },
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        style: {
                                                                            fontWeight: '700',
                                                                            marginBottom: '3px',
                                                                            fontSize: '13px' // Slightly larger for product names
                                                                        },
                                                                        children: item.description
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                        lineNumber: 1625,
                                                                        columnNumber: 53
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        style: {
                                                                            display: 'flex',
                                                                            justifyContent: 'space-between',
                                                                            fontSize: '12px',
                                                                            lineHeight: '1.3'
                                                                        },
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                                children: [
                                                                                    item.quantity,
                                                                                    " x ₦",
                                                                                    item.unitPrice.toLocaleString()
                                                                                ]
                                                                            }, void 0, true, {
                                                                                fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                                lineNumber: 1638,
                                                                                columnNumber: 57
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                                style: {
                                                                                    fontWeight: '700'
                                                                                },
                                                                                children: [
                                                                                    "₦",
                                                                                    item.total.toLocaleString()
                                                                                ]
                                                                            }, void 0, true, {
                                                                                fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                                lineNumber: 1639,
                                                                                columnNumber: 57
                                                                            }, this)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                        lineNumber: 1632,
                                                                        columnNumber: 53
                                                                    }, this)
                                                                ]
                                                            }, index, true, {
                                                                fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                lineNumber: 1617,
                                                                columnNumber: 49
                                                            }, this))
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                    lineNumber: 1604,
                                                    columnNumber: 41
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    style: {
                                                        borderTop: '2px dashed #000',
                                                        paddingTop: '8px',
                                                        fontSize: '13px',
                                                        fontWeight: '600',
                                                        color: '#000000'
                                                    },
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            style: {
                                                                display: 'flex',
                                                                justifyContent: 'space-between',
                                                                marginBottom: '4px',
                                                                lineHeight: '1.4'
                                                            },
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                                    children: "Subtotal:"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                    lineNumber: 1659,
                                                                    columnNumber: 49
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                                    children: [
                                                                        "₦",
                                                                        formData.subtotal.toLocaleString()
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                    lineNumber: 1660,
                                                                    columnNumber: 49
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                            lineNumber: 1653,
                                                            columnNumber: 45
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            style: {
                                                                display: 'flex',
                                                                justifyContent: 'space-between',
                                                                marginBottom: '4px',
                                                                lineHeight: '1.4'
                                                            },
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                                    children: "Amount Paid:"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                    lineNumber: 1668,
                                                                    columnNumber: 49
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                                    children: [
                                                                        "₦",
                                                                        formData.amountPaid.toLocaleString()
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                    lineNumber: 1669,
                                                                    columnNumber: 49
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                            lineNumber: 1662,
                                                            columnNumber: 45
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            style: {
                                                                display: 'flex',
                                                                justifyContent: 'space-between',
                                                                borderTop: '2px solid #000',
                                                                paddingTop: '6px',
                                                                marginTop: '6px',
                                                                fontSize: '14px',
                                                                fontWeight: '700'
                                                            },
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                                    children: "Balance:"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                    lineNumber: 1680,
                                                                    columnNumber: 49
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                                    style: {
                                                                        color: formData.balance > 0 ? '#cc0000' : '#008800'
                                                                    },
                                                                    children: [
                                                                        "₦",
                                                                        formData.balance.toLocaleString()
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                    lineNumber: 1681,
                                                                    columnNumber: 49
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                            lineNumber: 1671,
                                                            columnNumber: 45
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                    lineNumber: 1646,
                                                    columnNumber: 41
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    style: {
                                                        marginTop: '14px',
                                                        paddingTop: '8px',
                                                        borderTop: '2px dashed #000',
                                                        textAlign: 'center',
                                                        fontSize: '11px',
                                                        color: '#000000'
                                                    },
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            style: {
                                                                marginBottom: '4px',
                                                                fontWeight: '700',
                                                                fontSize: '12px' // Larger for thank you message
                                                            },
                                                            children: "Thank you for your patronage!"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                            lineNumber: 1696,
                                                            columnNumber: 45
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            style: {
                                                                marginBottom: '6px',
                                                                fontWeight: '600',
                                                                fontSize: '11px'
                                                            },
                                                            children: "Follow us: @cepoka"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                            lineNumber: 1703,
                                                            columnNumber: 45
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            style: {
                                                                fontSize: '9px',
                                                                color: '#666666',
                                                                fontWeight: '500'
                                                            },
                                                            children: "Computer-generated receipt"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                            lineNumber: 1710,
                                                            columnNumber: 45
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                    lineNumber: 1688,
                                                    columnNumber: 41
                                                }, this)
                                            ]
                                        }, void 0, true) : // BIG RECEIPT LAYOUT (A4) - Original layout
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "text-center mb-8",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "mb-3",
                                                            style: {
                                                                height: '80px',
                                                                position: 'relative'
                                                            },
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                                src: "/logo.png",
                                                                alt: "Cepoka Logo",
                                                                width: 80,
                                                                height: 80,
                                                                className: "mx-auto",
                                                                style: {
                                                                    maxWidth: '80px',
                                                                    height: 'auto',
                                                                    display: 'block',
                                                                    margin: '0 auto'
                                                                },
                                                                priority: true,
                                                                unoptimized: true
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                lineNumber: 1724,
                                                                columnNumber: 49
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                            lineNumber: 1723,
                                                            columnNumber: 45
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "relative pb-3 mb-6",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                                                    style: {
                                                                        fontSize: '24pt',
                                                                        fontWeight: '700',
                                                                        color: '#000000',
                                                                        marginBottom: '8px'
                                                                    },
                                                                    children: "CEPOKA BEAUTY HUB"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                    lineNumber: 1741,
                                                                    columnNumber: 49
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "absolute bottom-0 left-1/2 transform -translate-x-1/2 w-48 h-[2px]",
                                                                    style: {
                                                                        background: '#000000',
                                                                        height: '2px'
                                                                    }
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                    lineNumber: 1749,
                                                                    columnNumber: 49
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                            lineNumber: 1740,
                                                            columnNumber: 45
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "flex flex-col items-center",
                                                            style: {
                                                                marginTop: '12px',
                                                                marginBottom: '16px',
                                                                fontSize: '9pt',
                                                                color: '#000000'
                                                            },
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "mb-3 text-center",
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                            style: {
                                                                                fontWeight: '700',
                                                                                marginBottom: '4px',
                                                                                fontSize: '10pt'
                                                                            },
                                                                            children: "HEAD OFFICE/SHOWROOM"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                            lineNumber: 1766,
                                                                            columnNumber: 53
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                            className: "flex flex-wrap justify-center",
                                                                            children: [
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                                    style: {
                                                                                        padding: '0 8px'
                                                                                    },
                                                                                    children: "Lekki, Lagos"
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                                    lineNumber: 1772,
                                                                                    columnNumber: 57
                                                                                }, this),
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                                    style: {
                                                                                        padding: '0 8px'
                                                                                    },
                                                                                    children: "+234 803 123 4567"
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                                    lineNumber: 1773,
                                                                                    columnNumber: 57
                                                                                }, this),
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                                    style: {
                                                                                        padding: '0 8px'
                                                                                    },
                                                                                    children: "<EMAIL>"
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                                    lineNumber: 1774,
                                                                                    columnNumber: 57
                                                                                }, this)
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                            lineNumber: 1771,
                                                                            columnNumber: 53
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                    lineNumber: 1765,
                                                                    columnNumber: 49
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "text-center",
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                            style: {
                                                                                fontWeight: '700',
                                                                                marginBottom: '4px',
                                                                                fontSize: '10pt'
                                                                            },
                                                                            children: "BRANCHES"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                            lineNumber: 1780,
                                                                            columnNumber: 53
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                            className: "flex flex-wrap justify-center",
                                                                            children: [
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                                    style: {
                                                                                        padding: '0 8px'
                                                                                    },
                                                                                    children: "Ikeja, Lagos"
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                                    lineNumber: 1786,
                                                                                    columnNumber: 57
                                                                                }, this),
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                                    style: {
                                                                                        padding: '0 8px'
                                                                                    },
                                                                                    children: "Abuja"
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                                    lineNumber: 1787,
                                                                                    columnNumber: 57
                                                                                }, this)
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                            lineNumber: 1785,
                                                                            columnNumber: 53
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                    lineNumber: 1779,
                                                                    columnNumber: 49
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                            lineNumber: 1758,
                                                            columnNumber: 45
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                    lineNumber: 1722,
                                                    columnNumber: 41
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "mb-6",
                                                    style: {
                                                        padding: '12px',
                                                        backgroundColor: '#f8f8f8',
                                                        borderRadius: '8px'
                                                    },
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "flex flex-col xs:flex-row justify-between mb-2",
                                                            style: {
                                                                color: '#000000'
                                                            },
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                    className: "mb-1 xs:mb-0",
                                                                    style: {
                                                                        color: '#000000'
                                                                    },
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                                            style: {
                                                                                fontWeight: '600'
                                                                            },
                                                                            children: "Customer:"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                            lineNumber: 1796,
                                                                            columnNumber: 53
                                                                        }, this),
                                                                        " ",
                                                                        formData.customerName
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                    lineNumber: 1795,
                                                                    columnNumber: 49
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                    className: "mb-1 xs:mb-0",
                                                                    style: {
                                                                        color: '#000000'
                                                                    },
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                                            style: {
                                                                                fontWeight: '600'
                                                                            },
                                                                            children: "Receipt #:"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                            lineNumber: 1799,
                                                                            columnNumber: 53
                                                                        }, this),
                                                                        " ",
                                                                        formData.receiptNumber
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                    lineNumber: 1798,
                                                                    columnNumber: 49
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                            lineNumber: 1794,
                                                            columnNumber: 45
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "flex flex-col xs:flex-row xs:justify-between",
                                                            style: {
                                                                color: '#000000'
                                                            },
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                    className: "mb-1 xs:mb-0",
                                                                    style: {
                                                                        color: '#000000'
                                                                    },
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                                            style: {
                                                                                fontWeight: '600'
                                                                            },
                                                                            children: "Tel:"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                            lineNumber: 1804,
                                                                            columnNumber: 53
                                                                        }, this),
                                                                        " ",
                                                                        formData.whatsapp
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                    lineNumber: 1803,
                                                                    columnNumber: 49
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                    style: {
                                                                        color: '#000000'
                                                                    },
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                                            style: {
                                                                                fontWeight: '600'
                                                                            },
                                                                            children: "Date:"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                            lineNumber: 1807,
                                                                            columnNumber: 53
                                                                        }, this),
                                                                        " ",
                                                                        new Date(formData.date).toLocaleDateString('en-NG', {
                                                                            year: 'numeric',
                                                                            month: 'long',
                                                                            day: 'numeric'
                                                                        })
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                    lineNumber: 1806,
                                                                    columnNumber: 49
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                            lineNumber: 1802,
                                                            columnNumber: 45
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                    lineNumber: 1793,
                                                    columnNumber: 41
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "text-center mb-3",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                        style: {
                                                            fontWeight: '700',
                                                            fontSize: '14pt',
                                                            color: '#000000',
                                                            marginBottom: '8px'
                                                        },
                                                        children: "CASH SALES INVOICE"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                        lineNumber: 1818,
                                                        columnNumber: 45
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                    lineNumber: 1817,
                                                    columnNumber: 41
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "overflow-x-auto -mx-2 px-2",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("table", {
                                                        style: {
                                                            width: '100%',
                                                            borderCollapse: 'collapse',
                                                            marginBottom: '16px',
                                                            color: '#000000',
                                                            fontSize: '9pt',
                                                            minWidth: '300px'
                                                        },
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("thead", {
                                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                                                                    style: {
                                                                        borderBottom: '2px solid #000000',
                                                                        borderTop: '2px solid #000000'
                                                                    },
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                                            style: {
                                                                                padding: '8px 6px',
                                                                                textAlign: 'left',
                                                                                fontWeight: '700',
                                                                                color: '#000000'
                                                                            },
                                                                            children: "Description"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                            lineNumber: 1837,
                                                                            columnNumber: 57
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                                            style: {
                                                                                padding: '8px 4px',
                                                                                textAlign: 'right',
                                                                                fontWeight: '700',
                                                                                color: '#000000'
                                                                            },
                                                                            children: "Qty"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                            lineNumber: 1843,
                                                                            columnNumber: 57
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                                            style: {
                                                                                padding: '8px 4px',
                                                                                textAlign: 'right',
                                                                                fontWeight: '700',
                                                                                color: '#000000'
                                                                            },
                                                                            children: "Price"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                            lineNumber: 1849,
                                                                            columnNumber: 57
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                                            style: {
                                                                                padding: '8px 4px',
                                                                                textAlign: 'right',
                                                                                fontWeight: '700',
                                                                                color: '#000000'
                                                                            },
                                                                            children: "Total"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                            lineNumber: 1855,
                                                                            columnNumber: 57
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                    lineNumber: 1836,
                                                                    columnNumber: 53
                                                                }, this)
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                lineNumber: 1835,
                                                                columnNumber: 49
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("tbody", {
                                                                children: formData.items.map((item, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                                                                        style: {
                                                                            borderBottom: '1px solid #e5e5e5'
                                                                        },
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                                                style: {
                                                                                    padding: '8px 6px',
                                                                                    color: '#000000'
                                                                                },
                                                                                children: item.description
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                                lineNumber: 1866,
                                                                                columnNumber: 61
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                                                style: {
                                                                                    padding: '8px 4px',
                                                                                    textAlign: 'right',
                                                                                    color: '#000000'
                                                                                },
                                                                                children: item.quantity
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                                lineNumber: 1867,
                                                                                columnNumber: 61
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                                                style: {
                                                                                    padding: '8px 4px',
                                                                                    textAlign: 'right',
                                                                                    color: '#000000'
                                                                                },
                                                                                children: [
                                                                                    "₦",
                                                                                    item.unitPrice.toLocaleString()
                                                                                ]
                                                                            }, void 0, true, {
                                                                                fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                                lineNumber: 1868,
                                                                                columnNumber: 61
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                                                style: {
                                                                                    padding: '8px 4px',
                                                                                    textAlign: 'right',
                                                                                    color: '#000000'
                                                                                },
                                                                                children: [
                                                                                    "₦",
                                                                                    item.total.toLocaleString()
                                                                                ]
                                                                            }, void 0, true, {
                                                                                fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                                lineNumber: 1869,
                                                                                columnNumber: 61
                                                                            }, this)
                                                                        ]
                                                                    }, index, true, {
                                                                        fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                        lineNumber: 1865,
                                                                        columnNumber: 57
                                                                    }, this))
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                lineNumber: 1863,
                                                                columnNumber: 49
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                        lineNumber: 1827,
                                                        columnNumber: 45
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                    lineNumber: 1826,
                                                    columnNumber: 41
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    style: {
                                                        borderTop: '2px solid #000000',
                                                        paddingTop: '12px',
                                                        color: '#000000'
                                                    },
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex flex-col space-y-2",
                                                        style: {
                                                            color: '#000000'
                                                        },
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "flex justify-between items-center",
                                                                style: {
                                                                    color: '#000000'
                                                                },
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                                        style: {
                                                                            fontWeight: '700',
                                                                            fontSize: '12pt',
                                                                            color: '#000000'
                                                                        },
                                                                        children: "Subtotal:"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                        lineNumber: 1883,
                                                                        columnNumber: 53
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        style: {
                                                                            fontWeight: '700',
                                                                            fontSize: '12pt',
                                                                            color: '#000000'
                                                                        },
                                                                        children: [
                                                                            "₦",
                                                                            formData.subtotal.toLocaleString()
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                        lineNumber: 1888,
                                                                        columnNumber: 53
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                lineNumber: 1882,
                                                                columnNumber: 49
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "flex justify-between items-center",
                                                                style: {
                                                                    color: '#000000'
                                                                },
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                                        style: {
                                                                            fontWeight: '700',
                                                                            fontSize: '12pt',
                                                                            color: '#000000'
                                                                        },
                                                                        children: "Amount Paid:"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                        lineNumber: 1896,
                                                                        columnNumber: 53
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        style: {
                                                                            fontWeight: '700',
                                                                            fontSize: '12pt',
                                                                            color: '#000000'
                                                                        },
                                                                        children: [
                                                                            "₦",
                                                                            formData.amountPaid.toLocaleString()
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                        lineNumber: 1901,
                                                                        columnNumber: 53
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                lineNumber: 1895,
                                                                columnNumber: 49
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "flex justify-between items-center mt-3 pt-2 border-t border-gray-200",
                                                                style: {
                                                                    color: '#000000'
                                                                },
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                                        style: {
                                                                            fontWeight: '700',
                                                                            fontSize: '12pt',
                                                                            color: '#000000'
                                                                        },
                                                                        children: "Balance:"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                        lineNumber: 1909,
                                                                        columnNumber: 53
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        style: {
                                                                            fontWeight: '700',
                                                                            fontSize: '12pt',
                                                                            color: formData.balance > 0 ? '#cc0000' : '#008800'
                                                                        },
                                                                        children: [
                                                                            "₦",
                                                                            formData.balance.toLocaleString()
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                        lineNumber: 1914,
                                                                        columnNumber: 53
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                lineNumber: 1908,
                                                                columnNumber: 49
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                        lineNumber: 1881,
                                                        columnNumber: 45
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                    lineNumber: 1876,
                                                    columnNumber: 41
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    style: {
                                                        marginTop: '16px',
                                                        paddingTop: '12px',
                                                        borderTop: '1px solid #e5e5e5',
                                                        textAlign: 'center',
                                                        color: '#000000',
                                                        fontSize: '9pt'
                                                    },
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            style: {
                                                                marginBottom: '3px',
                                                                fontWeight: '500',
                                                                color: '#000000'
                                                            },
                                                            children: "Thank you for your patronage!"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                            lineNumber: 1931,
                                                            columnNumber: 45
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            style: {
                                                                fontWeight: '500',
                                                                color: '#000000'
                                                            },
                                                            children: "Follow us on Instagram: @cepoka"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                            lineNumber: 1936,
                                                            columnNumber: 45
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            style: {
                                                                marginTop: '12px',
                                                                fontSize: '8pt',
                                                                color: '#666666'
                                                            },
                                                            children: "This is a computer-generated receipt and requires no signature."
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                            lineNumber: 1940,
                                                            columnNumber: 45
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                    lineNumber: 1923,
                                                    columnNumber: 41
                                                }, this)
                                            ]
                                        }, void 0, true)
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                        lineNumber: 1492,
                                        columnNumber: 29
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "sticky bottom-0 bg-white pt-3 pb-1 border-t mt-4",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex flex-col sm:flex-row gap-2 sm:gap-4",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                    onClick: downloadPDF,
                                                    className: "w-full sm:flex-1 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 text-sm sm:text-base",
                                                    children: "Download PDF"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                    lineNumber: 1954,
                                                    columnNumber: 37
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                    onClick: sendWhatsApp,
                                                    className: "w-full sm:flex-1 bg-[#25D366] text-white px-4 py-2 rounded-lg hover:bg-[#128C7E] text-sm sm:text-base",
                                                    children: "Send WhatsApp"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                    lineNumber: 1960,
                                                    columnNumber: 37
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                    onClick: startNewReceipt,
                                                    className: "w-full sm:flex-1 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 text-sm sm:text-base",
                                                    children: "New Receipt"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                    lineNumber: 1966,
                                                    columnNumber: 37
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                            lineNumber: 1953,
                                            columnNumber: 33
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                        lineNumber: 1952,
                                        columnNumber: 29
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                lineNumber: 1486,
                                columnNumber: 25
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                        lineNumber: 1246,
                        columnNumber: 17
                    }, this),
                    showSizeModal && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                            initial: {
                                opacity: 0,
                                scale: 0.9
                            },
                            animate: {
                                opacity: 1,
                                scale: 1
                            },
                            exit: {
                                opacity: 0,
                                scale: 0.9
                            },
                            className: "bg-white rounded-xl shadow-2xl max-w-md w-full p-6",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-center mb-6",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                            className: "text-xl font-bold text-gray-900 mb-2",
                                            children: "Choose Receipt Size"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                            lineNumber: 1988,
                                            columnNumber: 33
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-gray-600 text-sm",
                                            children: "What size of receipt do you want to generate?"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                            lineNumber: 1991,
                                            columnNumber: 33
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                    lineNumber: 1987,
                                    columnNumber: 29
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "space-y-4",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            onClick: ()=>handleSizeSelection('big'),
                                            className: "w-full p-4 border-2 border-gray-200 rounded-lg hover:border-blue-500 hover:bg-blue-50 transition-all duration-200 text-left group",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex items-center justify-between",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                                className: "font-semibold text-gray-900 group-hover:text-blue-700",
                                                                children: "📄 Big Receipt (A4)"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                lineNumber: 2004,
                                                                columnNumber: 45
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                className: "text-sm text-gray-600 mt-1",
                                                                children: "Standard A4 size - Current default format"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                lineNumber: 2007,
                                                                columnNumber: 45
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                        lineNumber: 2003,
                                                        columnNumber: 41
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "text-2xl group-hover:scale-110 transition-transform",
                                                        children: "📄"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                        lineNumber: 2011,
                                                        columnNumber: 41
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                lineNumber: 2002,
                                                columnNumber: 37
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                            lineNumber: 1998,
                                            columnNumber: 33
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            onClick: ()=>handleSizeSelection('mini'),
                                            className: "w-full p-4 border-2 border-gray-200 rounded-lg hover:border-green-500 hover:bg-green-50 transition-all duration-200 text-left group",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex items-center justify-between",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                                className: "font-semibold text-gray-900 group-hover:text-green-700",
                                                                children: "🧾 Mini Receipt (Envelope #9)"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                lineNumber: 2024,
                                                                columnNumber: 45
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                className: "text-sm text-gray-600 mt-1",
                                                                children: 'For thermal printers - Envelope #9 size (3.875" x 8.875")'
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                                lineNumber: 2027,
                                                                columnNumber: 45
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                        lineNumber: 2023,
                                                        columnNumber: 41
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "text-2xl group-hover:scale-110 transition-transform",
                                                        children: "🧾"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                        lineNumber: 2031,
                                                        columnNumber: 41
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                                lineNumber: 2022,
                                                columnNumber: 37
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                            lineNumber: 2018,
                                            columnNumber: 33
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                    lineNumber: 1996,
                                    columnNumber: 29
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "mt-6 pt-4 border-t border-gray-200",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                        onClick: ()=>setShowSizeModal(false),
                                        className: "w-full px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",
                                        children: "Cancel"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                        lineNumber: 2039,
                                        columnNumber: 33
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                                    lineNumber: 2038,
                                    columnNumber: 29
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                            lineNumber: 1981,
                            columnNumber: 25
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                        lineNumber: 1980,
                        columnNumber: 21
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
                lineNumber: 1213,
                columnNumber: 13
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/admin/receipt-sender/page.tsx",
        lineNumber: 1210,
        columnNumber: 9
    }, this);
};
const __TURBOPACK__default__export__ = ReceiptSender;
}}),
"[project]/src/app/admin/receipt-sender/page.tsx [app-rsc] (ecmascript, Next.js server component, client modules ssr)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
}}),

};

//# sourceMappingURL=src_4b7638._.js.map